#!/usr/bin/env python3
"""
Test Real Data Integration - Verify all services work with actual APIs
"""

import asyncio
import os
from datetime import datetime


async def test_real_token_discovery():
    """Test real token discovery service"""
    print("🔍 Testing Real Token Discovery...")
    
    try:
        from services.data_sources.real_token_discovery import RealTokenDiscoveryService
        
        async with RealTokenDiscoveryService() as discovery:
            # Test CoinGecko discovery
            coingecko_tokens = await discovery.discover_from_coingecko(limit=3)
            print(f"✅ CoinGecko: {len(coingecko_tokens)} tokens discovered")
            
            # Test DexScreener discovery
            dex_tokens = await discovery.discover_from_dexscreener("ethereum")
            print(f"✅ DexScreener: {len(dex_tokens)} tokens discovered")
            
            # Test complete discovery
            all_tokens = await discovery.discover_new_tokens(["ethereum"])
            print(f"✅ Total unique tokens: {len(all_tokens)}")
            
            if all_tokens:
                sample_token = all_tokens[0]
                print(f"   Sample: {sample_token.get('symbol', 'N/A')} - ${sample_token.get('price_usd', 0):.6f}")
            
            return len(all_tokens) > 0
            
    except Exception as e:
        print(f"❌ Token discovery failed: {e}")
        return False


async def test_real_contract_analysis():
    """Test real contract analysis service"""
    print("🔒 Testing Real Contract Analysis...")
    
    try:
        from services.data_sources.real_contract_analysis import RealContractAnalysisService
        
        # Test with USDC contract (known to be verified)
        usdc_contract = "******************************************"
        
        async with RealContractAnalysisService() as analyzer:
            # Test source code retrieval
            source_data = await analyzer.get_contract_source_code(usdc_contract)
            if source_data:
                print(f"✅ Source code: {source_data.get('contract_name', 'Unknown')}")
            else:
                print("⚠️  No source code (may need valid Etherscan API key)")
            
            # Test ABI retrieval
            abi_data = await analyzer.get_contract_abi(usdc_contract)
            if abi_data:
                print(f"✅ ABI: {len(abi_data)} functions/events")
            else:
                print("⚠️  No ABI found")
            
            # Test complete analysis
            analysis = await analyzer.analyze_contract(usdc_contract)
            print(f"✅ Analysis: Risk score {analysis['risk_score']:.2f}")
            print(f"   Risk factors: {len(analysis['risk_factors'])}")
            
            return True
            
    except Exception as e:
        print(f"❌ Contract analysis failed: {e}")
        return False


async def test_real_onchain_data():
    """Test real on-chain data service"""
    print("📊 Testing Real On-Chain Data...")
    
    try:
        from services.data_sources.real_onchain_data import RealOnChainDataService
        
        # Test with a known token contract
        test_contract = "******************************************"
        
        async with RealOnChainDataService() as analyzer:
            # Test token transfers
            transfers = await analyzer.get_token_transfers(test_contract, 5)
            print(f"✅ Transfers: {len(transfers)} retrieved")
            
            # Test holder analysis
            holders = await analyzer.get_token_holders(test_contract, 10)
            print(f"✅ Holders: {len(holders)} identified")
            
            # Test complete analysis
            analysis = await analyzer.analyze_onchain_behavior(test_contract)
            print(f"✅ On-chain analysis: Risk score {analysis['overall_risk_score']:.2f}")
            print(f"   Confidence: {analysis['confidence']:.2f}")
            
            return True
            
    except Exception as e:
        print(f"❌ On-chain analysis failed: {e}")
        return False


async def test_real_social_data():
    """Test real social media data service"""
    print("📱 Testing Real Social Media Data...")
    
    try:
        from services.data_sources.real_social_data import RealSocialDataService
        
        async with RealSocialDataService() as analyzer:
            # Test Reddit search
            reddit_posts = await analyzer.search_reddit_posts("bitcoin", 3)
            print(f"✅ Reddit: {len(reddit_posts)} posts retrieved")
            
            # Test Twitter search
            twitter_posts = await analyzer.search_twitter_posts("bitcoin", 3)
            print(f"✅ Twitter: {len(twitter_posts)} posts retrieved")
            
            # Test sentiment analysis
            test_content = "Bitcoin is going to the moon! 🚀 Diamond hands!"
            sentiment = analyzer.analyze_post_sentiment(test_content)
            print(f"✅ Sentiment: {sentiment['overall_sentiment']} (confidence: {sentiment['confidence']:.2f})")
            
            # Test complete social analysis
            analysis = await analyzer.analyze_social_sentiment("BTC")
            print(f"✅ Social analysis: {analysis['posts_analyzed']} posts analyzed")
            print(f"   Risk score: {analysis['overall_risk_score']:.2f}")
            
            return True
            
    except Exception as e:
        print(f"❌ Social analysis failed: {e}")
        return False


async def test_deepseek_integration():
    """Test DeepSeek R1 model integration"""
    print("🧠 Testing DeepSeek R1 Integration...")
    
    try:
        from langchain_openai import ChatOpenAI
        from shared.config.settings import settings
        
        # Check if API key is configured
        if not settings.api.openrouter_api_key:
            print("⚠️  OpenRouter API key not configured")
            return False
        
        # Initialize DeepSeek R1 model
        llm = ChatOpenAI(
            model="deepseek/deepseek-r1-0528:free",
            openai_api_key=settings.api.openrouter_api_key,
            openai_api_base="https://openrouter.ai/api/v1",
            temperature=0.1,
            max_tokens=100
        )
        
        # Test simple query
        response = await llm.ainvoke("What is a rug pull in cryptocurrency? Answer in one sentence.")
        print(f"✅ DeepSeek R1 response: {response.content[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek integration failed: {e}")
        return False


async def test_agent_real_data_integration():
    """Test agents with real data integration"""
    print("🤖 Testing Agent Real Data Integration...")
    
    try:
        # Test Token Hunter with real data
        print("\n1. Testing Token Hunter Agent...")
        from services.langgraph-agents.token_hunter import TokenHunterAgent

        hunter = TokenHunterAgent()
        tokens = await hunter.hunt_tokens(chains=["ethereum"], max_tokens=3)
        print(f"✅ Token Hunter: {len(tokens)} tokens found")

        if tokens:
            sample_token = tokens[0]
            print(f"   Sample: {sample_token.get('symbol', 'N/A')} - Priority: {sample_token.get('priority', 'N/A')}")

        # Test Contract Auditor with real data
        if tokens:
            print("\n2. Testing Contract Auditor Agent...")
            from services.langgraph-agents.contract_auditor import ContractAuditorAgent

            auditor = ContractAuditorAgent()
            audit_result = await auditor.audit_contract(
                tokens[0].get('contract_address', '0x0'),
                tokens[0].get('chain', 'ethereum')
            )

            if audit_result:
                print(f"✅ Contract Auditor: Risk score {audit_result.get('overall_risk_score', 0):.2f}")
                print(f"   Vulnerabilities: {len(audit_result.get('vulnerabilities', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent integration failed: {e}")
        return False


async def check_api_configuration():
    """Check API configuration status"""
    print("⚙️  Checking API Configuration...")
    
    from shared.config.settings import settings
    
    api_status = {
        "OpenRouter": bool(settings.api.openrouter_api_key),
        "Etherscan": bool(settings.api.etherscan_api_key),
        "CoinGecko": bool(settings.api.coingecko_api_key),
        "Reddit": bool(settings.api.reddit_client_id and settings.api.reddit_client_secret),
        "Twitter": bool(settings.api.twitter_bearer_token),
    }
    
    print("\nAPI Configuration Status:")
    for api, configured in api_status.items():
        status = "✅ Configured" if configured else "❌ Not configured"
        print(f"  {api}: {status}")
    
    configured_count = sum(api_status.values())
    print(f"\nTotal: {configured_count}/{len(api_status)} APIs configured")
    
    if configured_count == 0:
        print("\n⚠️  WARNING: No APIs configured! Tests will use fallback/mock data.")
        print("   To enable real data integration, configure API keys in .env file:")
        print("   - OPENROUTER_API_KEY=your_key")
        print("   - ETHERSCAN_API_KEY=your_key")
        print("   - COINGECKO_API_KEY=your_key")
        print("   - REDDIT_CLIENT_ID=your_id")
        print("   - REDDIT_CLIENT_SECRET=your_secret")
        print("   - TWITTER_BEARER_TOKEN=your_token")
    
    return configured_count > 0


async def main():
    """Run all real data integration tests"""
    print("🚀 Real Data Integration Test Suite")
    print("=" * 60)
    print(f"📅 {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}")
    
    # Check API configuration first
    apis_configured = await check_api_configuration()
    
    print(f"\n{'='*60}")
    print("Running Real Data Integration Tests")
    print('='*60)
    
    tests = [
        ("Real Token Discovery", test_real_token_discovery),
        ("Real Contract Analysis", test_real_contract_analysis),
        ("Real On-Chain Data", test_real_onchain_data),
        ("Real Social Media Data", test_real_social_data),
        ("DeepSeek R1 Integration", test_deepseek_integration),
        ("Agent Real Data Integration", test_agent_real_data_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-'*40}")
        print(f"Testing: {test_name}")
        print('-'*40)
        
        try:
            result = await test_func()
            
            if result:
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*60}")
    print(f"TEST SUMMARY: {passed}/{total} tests passed")
    print('='*60)
    
    if passed == total:
        print("🎉 All real data integration tests passed!")
        print("\n✅ System ready with real data integration:")
        print("  • DeepSeek R1 model configured")
        print("  • Real API data sources integrated")
        print("  • All agents updated for production")
        print("  • Fallback mechanisms in place")
        
        if apis_configured:
            print("\n🔑 API Integration Status: ACTIVE")
        else:
            print("\n⚠️  API Integration Status: FALLBACK MODE")
            print("   Configure API keys for full real-data functionality")
        
        return True
    else:
        print(f"\n⚠️  {total - passed} tests failed. System may have issues.")
        print("   Check API configurations and network connectivity.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
