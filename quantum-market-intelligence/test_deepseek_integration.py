#!/usr/bin/env python3
"""
Test DeepSeek R1 Integration
"""

import asyncio
import os


async def test_deepseek_basic():
    """Test basic DeepSeek R1 functionality"""
    print("🧠 Testing DeepSeek R1 Basic Integration...")
    
    try:
        from langchain_openai import Chat<PERSON>penAI
        
        # Check if API key is available
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            print("❌ OPENROUTER_API_KEY not found in environment")
            return False
        
        # Initialize DeepSeek R1 model
        llm = ChatOpenAI(
            model="deepseek/deepseek-r1-0528:free",
            openai_api_key=api_key,
            openai_api_base="https://openrouter.ai/api/v1",
            temperature=0.1,
            max_tokens=100
        )
        
        # Test simple query
        print("Sending test query to DeepSeek R1...")
        response = await llm.ainvoke("What is a rug pull in cryptocurrency? Answer in one sentence.")
        
        print(f"✅ DeepSeek R1 Response: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek integration failed: {e}")
        return False


async def test_deepseek_crypto_analysis():
    """Test DeepSeek R1 with crypto-specific analysis"""
    print("\n🔍 Testing DeepSeek R1 Crypto Analysis...")
    
    try:
        from langchain_openai import ChatOpenAI
        
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            print("❌ OPENROUTER_API_KEY not found")
            return False
        
        llm = ChatOpenAI(
            model="deepseek/deepseek-r1-0528:free",
            openai_api_key=api_key,
            openai_api_base="https://openrouter.ai/api/v1",
            temperature=0.1,
            max_tokens=200
        )
        
        # Test crypto analysis query
        query = """
        Analyze this smart contract function for potential risks:
        
        function mint(address to, uint256 amount) public onlyOwner {
            _totalSupply += amount;
            _balances[to] += amount;
            emit Transfer(address(0), to, amount);
        }
        
        List the top 3 risks in bullet points.
        """
        
        print("Analyzing smart contract with DeepSeek R1...")
        response = await llm.ainvoke(query)
        
        print(f"✅ DeepSeek R1 Analysis:")
        print(response.content)
        
        return True
        
    except Exception as e:
        print(f"❌ Crypto analysis failed: {e}")
        return False


async def test_deepseek_sentiment_analysis():
    """Test DeepSeek R1 with sentiment analysis"""
    print("\n📱 Testing DeepSeek R1 Sentiment Analysis...")
    
    try:
        from langchain_openai import ChatOpenAI
        
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            print("❌ OPENROUTER_API_KEY not found")
            return False
        
        llm = ChatOpenAI(
            model="deepseek/deepseek-r1-0528:free",
            openai_api_key=api_key,
            openai_api_base="https://openrouter.ai/api/v1",
            temperature=0.1,
            max_tokens=150
        )
        
        # Test sentiment analysis
        query = """
        Analyze the sentiment of these crypto social media posts:
        
        1. "DOGE to the moon! 🚀🚀🚀 Diamond hands! HODL!"
        2. "Warning: This token looks like a rug pull. Avoid!"
        3. "Just bought the dip. Great fundamentals on this project."
        
        Rate each as: very_positive, positive, neutral, negative, or very_negative
        """
        
        print("Analyzing sentiment with DeepSeek R1...")
        response = await llm.ainvoke(query)
        
        print(f"✅ DeepSeek R1 Sentiment Analysis:")
        print(response.content)
        
        return True
        
    except Exception as e:
        print(f"❌ Sentiment analysis failed: {e}")
        return False


async def main():
    """Run DeepSeek integration tests"""
    print("🚀 DeepSeek R1 Integration Test Suite")
    print("=" * 50)
    
    tests = [
        ("Basic Integration", test_deepseek_basic),
        ("Crypto Analysis", test_deepseek_crypto_analysis),
        ("Sentiment Analysis", test_deepseek_sentiment_analysis),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-'*30}")
        print(f"Testing: {test_name}")
        print('-'*30)
        
        try:
            result = await test_func()
            
            if result:
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*50}")
    print(f"TEST SUMMARY: {passed}/{total} tests passed")
    print('='*50)
    
    if passed == total:
        print("🎉 DeepSeek R1 integration working perfectly!")
        print("\n✅ Capabilities verified:")
        print("  • Basic LLM functionality")
        print("  • Smart contract analysis")
        print("  • Crypto sentiment analysis")
        print("  • OpenRouter API integration")
        return True
    else:
        print(f"\n⚠️  {total - passed} tests failed.")
        print("   Check OpenRouter API key and network connectivity.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
