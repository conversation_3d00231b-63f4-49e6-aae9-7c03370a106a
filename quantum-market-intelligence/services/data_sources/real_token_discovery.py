"""
Real Token Discovery Service - Integrates with actual APIs
"""

import asyncio
import aiohttp
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging

try:
    from shared.config.settings import settings
    from shared.utils.logging import get_logger
except ImportError:
    # Fallback for testing
    class MockSettings:
        class API:
            coingecko_api_key = None
            dexscreener_api_key = None
            etherscan_api_key = None
        api = API()
    settings = MockSettings()
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    def get_logger(name): return logger

logger = get_logger(__name__)


class RealTokenDiscoveryService:
    """
    Real token discovery service using actual APIs
    """
    
    def __init__(self):
        self.session = None
        self.rate_limits = {
            "coingecko": {"calls": 0, "reset_time": datetime.utcnow()},
            "dexscreener": {"calls": 0, "reset_time": datetime.utcnow()},
            "etherscan": {"calls": 0, "reset_time": datetime.utcnow()}
        }
        
        # Rate limit thresholds (calls per minute)
        self.rate_limit_thresholds = {
            "coingecko": 50,  # Free tier: 50 calls/minute
            "dexscreener": 300,  # Public API: 300 calls/minute
            "etherscan": 200  # Free tier: 200 calls/minute
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                "User-Agent": "Quantum-Market-Intelligence/1.0",
                "Accept": "application/json"
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    def _check_rate_limit(self, service: str) -> bool:
        """Check if we can make a request to the service"""
        now = datetime.utcnow()
        rate_info = self.rate_limits[service]
        
        # Reset counter if a minute has passed
        if now - rate_info["reset_time"] > timedelta(minutes=1):
            rate_info["calls"] = 0
            rate_info["reset_time"] = now
        
        # Check if we're under the limit
        return rate_info["calls"] < self.rate_limit_thresholds[service]
    
    def _increment_rate_limit(self, service: str):
        """Increment the rate limit counter"""
        self.rate_limits[service]["calls"] += 1
    
    async def discover_from_coingecko(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Discover new tokens from CoinGecko"""
        if not self._check_rate_limit("coingecko"):
            logger.warning("CoinGecko rate limit reached")
            return []
        
        try:
            url = "https://api.coingecko.com/api/v3/coins/markets"
            params = {
                "vs_currency": "usd",
                "order": "volume_desc",
                "per_page": limit,
                "page": 1,
                "sparkline": "false",
                "price_change_percentage": "24h"
            }
            
            # Add API key if available
            if settings.api.coingecko_api_key:
                params["x_cg_demo_api_key"] = settings.api.coingecko_api_key
            
            self._increment_rate_limit("coingecko")
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    tokens = []
                    for coin in data:
                        # Filter for recently listed or high volume tokens
                        if (coin.get("total_volume", 0) > 100000 and 
                            coin.get("market_cap_rank") and 
                            coin.get("market_cap_rank") > 100):  # Focus on newer tokens
                            
                            tokens.append({
                                "contract_address": coin.get("id"),  # CoinGecko ID
                                "symbol": coin.get("symbol", "").upper(),
                                "name": coin.get("name", ""),
                                "chain": "ethereum",  # Default, would need chain detection
                                "price_usd": coin.get("current_price", 0),
                                "market_cap": coin.get("market_cap", 0),
                                "volume_24h": coin.get("total_volume", 0),
                                "price_change_24h": coin.get("price_change_percentage_24h", 0),
                                "source": "coingecko",
                                "discovered_at": datetime.utcnow().isoformat()
                            })
                    
                    logger.info(f"Discovered {len(tokens)} tokens from CoinGecko")
                    return tokens
                
                else:
                    logger.error(f"CoinGecko API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error fetching from CoinGecko: {e}")
            return []
    
    async def discover_from_dexscreener(self, chain: str = "ethereum") -> List[Dict[str, Any]]:
        """Discover new tokens from DexScreener"""
        if not self._check_rate_limit("dexscreener"):
            logger.warning("DexScreener rate limit reached")
            return []
        
        try:
            # Get trending tokens - use search endpoint instead
            url = f"https://api.dexscreener.com/latest/dex/search/?q={chain}"
            
            self._increment_rate_limit("dexscreener")
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    tokens = []
                    for pair in data.get("pairs", [])[:20]:  # Limit to top 20
                        base_token = pair.get("baseToken", {})
                        
                        # Filter for quality tokens
                        liquidity = pair.get("liquidity", {}).get("usd", 0)
                        volume_24h = pair.get("volume", {}).get("h24", 0)
                        
                        if liquidity > 50000 and volume_24h > 10000:  # Minimum thresholds
                            tokens.append({
                                "contract_address": base_token.get("address", ""),
                                "symbol": base_token.get("symbol", "").upper(),
                                "name": base_token.get("name", ""),
                                "chain": chain,
                                "price_usd": float(pair.get("priceUsd", 0)),
                                "liquidity_usd": liquidity,
                                "volume_24h": volume_24h,
                                "price_change_24h": pair.get("priceChange", {}).get("h24", 0),
                                "dex": pair.get("dexId", ""),
                                "pair_address": pair.get("pairAddress", ""),
                                "source": "dexscreener",
                                "discovered_at": datetime.utcnow().isoformat()
                            })
                    
                    logger.info(f"Discovered {len(tokens)} tokens from DexScreener")
                    return tokens
                
                else:
                    logger.error(f"DexScreener API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Error fetching from DexScreener: {e}")
            return []
    
    async def get_token_details_from_etherscan(self, contract_address: str) -> Optional[Dict[str, Any]]:
        """Get detailed token information from Etherscan"""
        if not settings.api.etherscan_api_key:
            logger.warning("Etherscan API key not configured")
            return None
        
        if not self._check_rate_limit("etherscan"):
            logger.warning("Etherscan rate limit reached")
            return None
        
        try:
            # Get token info
            url = "https://api.etherscan.io/api"
            params = {
                "module": "token",
                "action": "tokeninfo",
                "contractaddress": contract_address,
                "apikey": settings.api.etherscan_api_key
            }
            
            self._increment_rate_limit("etherscan")
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if data.get("status") == "1" and data.get("result"):
                        result = data["result"][0] if isinstance(data["result"], list) else data["result"]
                        
                        return {
                            "contract_address": contract_address,
                            "symbol": result.get("symbol", ""),
                            "name": result.get("tokenName", ""),
                            "decimals": int(result.get("divisor", 18)),
                            "total_supply": result.get("totalSupply", "0"),
                            "verified": True,  # If we got data, contract is verified
                            "source": "etherscan"
                        }
                
                logger.warning(f"No token info found for {contract_address}")
                return None
                
        except Exception as e:
            logger.error(f"Error fetching token details from Etherscan: {e}")
            return None
    
    async def discover_new_tokens(self, chains: List[str] = ["ethereum"]) -> List[Dict[str, Any]]:
        """Discover new tokens from multiple sources"""
        all_tokens = []
        
        # Discover from CoinGecko
        coingecko_tokens = await self.discover_from_coingecko()
        all_tokens.extend(coingecko_tokens)
        
        # Discover from DexScreener for each chain
        for chain in chains:
            dex_tokens = await self.discover_from_dexscreener(chain)
            all_tokens.extend(dex_tokens)
        
        # Deduplicate by contract address
        seen_addresses = set()
        unique_tokens = []
        
        for token in all_tokens:
            address = token.get("contract_address", "").lower()
            if address and address not in seen_addresses:
                seen_addresses.add(address)
                unique_tokens.append(token)
        
        logger.info(f"Discovered {len(unique_tokens)} unique tokens from {len(all_tokens)} total")
        return unique_tokens
    
    async def enrich_token_data(self, token: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich token data with additional information"""
        contract_address = token.get("contract_address", "")
        
        # Skip if not a valid Ethereum address
        if not contract_address.startswith("0x") or len(contract_address) != 42:
            return token
        
        # Get additional details from Etherscan
        etherscan_data = await self.get_token_details_from_etherscan(contract_address)
        
        if etherscan_data:
            # Merge data, preferring Etherscan for accuracy
            token.update({
                "symbol": etherscan_data.get("symbol", token.get("symbol", "")),
                "name": etherscan_data.get("name", token.get("name", "")),
                "decimals": etherscan_data.get("decimals", 18),
                "total_supply": etherscan_data.get("total_supply", "0"),
                "verified": etherscan_data.get("verified", False),
                "enriched": True
            })
        
        return token


async def test_real_token_discovery():
    """Test the real token discovery service"""
    print("🔍 Testing Real Token Discovery Service...")
    
    async with RealTokenDiscoveryService() as discovery:
        # Test CoinGecko discovery
        print("\n1. Testing CoinGecko Discovery...")
        coingecko_tokens = await discovery.discover_from_coingecko(limit=5)
        print(f"✅ Found {len(coingecko_tokens)} tokens from CoinGecko")
        
        if coingecko_tokens:
            print(f"Sample token: {coingecko_tokens[0]['symbol']} - ${coingecko_tokens[0]['price_usd']}")
        
        # Test DexScreener discovery
        print("\n2. Testing DexScreener Discovery...")
        dex_tokens = await discovery.discover_from_dexscreener("ethereum")
        print(f"✅ Found {len(dex_tokens)} tokens from DexScreener")
        
        if dex_tokens:
            print(f"Sample token: {dex_tokens[0]['symbol']} - Liquidity: ${dex_tokens[0]['liquidity_usd']:,.0f}")
        
        # Test token enrichment
        if dex_tokens:
            print("\n3. Testing Token Enrichment...")
            enriched_token = await discovery.enrich_token_data(dex_tokens[0])
            print(f"✅ Enriched token: {enriched_token.get('name', 'N/A')} ({enriched_token.get('symbol', 'N/A')})")
        
        # Test complete discovery
        print("\n4. Testing Complete Discovery...")
        all_tokens = await discovery.discover_new_tokens(["ethereum"])
        print(f"✅ Total unique tokens discovered: {len(all_tokens)}")
        
        return len(all_tokens) > 0


if __name__ == "__main__":
    success = asyncio.run(test_real_token_discovery())
    if success:
        print("\n🎉 Real token discovery service working!")
    else:
        print("\n❌ Real token discovery service failed!")
