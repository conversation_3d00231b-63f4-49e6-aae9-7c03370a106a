"""
Health check endpoints
"""

from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse

from shared.models.base import HealthCheckResponse
from shared.utils.cache import cache_manager
from shared.utils.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


async def check_database_health() -> Dict[str, Any]:
    """Check database connectivity"""
    try:
        # TODO: Add actual database health check
        return {"status": "healthy", "response_time_ms": 5}
    except Exception as e:
        logger.error("Database health check failed", error=str(e))
        return {"status": "unhealthy", "error": str(e)}


async def check_cache_health() -> Dict[str, Any]:
    """Check Redis cache connectivity"""
    try:
        start_time = datetime.utcnow()
        is_healthy = await cache_manager.health_check()
        end_time = datetime.utcnow()
        
        response_time_ms = (end_time - start_time).total_seconds() * 1000
        
        return {
            "status": "healthy" if is_healthy else "unhealthy",
            "response_time_ms": round(response_time_ms, 2)
        }
    except Exception as e:
        logger.error("Cache health check failed", error=str(e))
        return {"status": "unhealthy", "error": str(e)}


async def check_external_apis_health() -> Dict[str, Any]:
    """Check external API connectivity"""
    # TODO: Add actual external API health checks
    return {
        "coingecko": {"status": "healthy", "response_time_ms": 150},
        "etherscan": {"status": "healthy", "response_time_ms": 200},
        "dexscreener": {"status": "healthy", "response_time_ms": 100}
    }


@router.get("/", response_model=HealthCheckResponse)
async def health_check():
    """
    Basic health check endpoint
    
    Returns basic service status
    """
    return HealthCheckResponse(
        status="healthy",
        timestamp=datetime.utcnow(),
        version="1.0.0"
    )


@router.get("/detailed")
async def detailed_health_check():
    """
    Detailed health check with dependency status
    
    Returns comprehensive health information including:
    - Database connectivity
    - Cache connectivity  
    - External API status
    - System metrics
    """
    # Check all dependencies
    database_health = await check_database_health()
    cache_health = await check_cache_health()
    external_apis_health = await check_external_apis_health()
    
    # Get cache stats
    cache_stats = await cache_manager.get_stats()
    
    # Determine overall status
    all_healthy = all([
        database_health["status"] == "healthy",
        cache_health["status"] == "healthy",
        all(api["status"] == "healthy" for api in external_apis_health.values())
    ])
    
    overall_status = "healthy" if all_healthy else "degraded"
    status_code = 200 if all_healthy else 503
    
    response_data = {
        "status": overall_status,
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
        "services": {
            "database": database_health,
            "cache": cache_health,
            "external_apis": external_apis_health
        },
        "system": {
            "cache_stats": cache_stats,
            "uptime_seconds": 0,  # TODO: Add actual uptime tracking
        }
    }
    
    return JSONResponse(
        status_code=status_code,
        content=response_data
    )


@router.get("/ready")
async def readiness_check():
    """
    Kubernetes readiness probe endpoint
    
    Returns 200 if service is ready to accept traffic
    """
    try:
        # Check critical dependencies
        cache_healthy = await cache_manager.health_check()
        
        if cache_healthy:
            return {"status": "ready", "timestamp": datetime.utcnow().isoformat()}
        else:
            return JSONResponse(
                status_code=503,
                content={"status": "not_ready", "reason": "cache_unavailable"}
            )
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        return JSONResponse(
            status_code=503,
            content={"status": "not_ready", "reason": str(e)}
        )


@router.get("/live")
async def liveness_check():
    """
    Kubernetes liveness probe endpoint
    
    Returns 200 if service is alive (should restart if this fails)
    """
    return {"status": "alive", "timestamp": datetime.utcnow().isoformat()}


@router.get("/metrics")
async def health_metrics():
    """
    Health-related metrics endpoint
    
    Returns metrics about service health and performance
    """
    try:
        cache_stats = await cache_manager.get_stats()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "metrics": {
                "cache": cache_stats,
                "requests": {
                    "total": 0,  # TODO: Add actual request counting
                    "errors": 0,
                    "avg_response_time_ms": 0
                }
            }
        }
    except Exception as e:
        logger.error("Health metrics failed", error=str(e))
        return JSONResponse(
            status_code=500,
            content={"error": "Failed to collect metrics", "details": str(e)}
        )
