"""
Custom middleware for the API Gateway
"""

import time
import uuid
from typing import Callable

from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from shared.utils.logging import get_logger, request_logger
from shared.utils.observability import observability_manager
from shared.utils.cache import cache_manager, CacheKeys

logger = get_logger(__name__)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging HTTP requests"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate request ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Record start time
        start_time = time.time()
        
        # Process request
        response = await call_next(request)
        
        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000
        
        # Log request
        request_logger.log_request(
            method=request.method,
            path=request.url.path,
            status_code=response.status_code,
            duration_ms=duration_ms,
            request_id=request_id,
            user_agent=request.headers.get("user-agent"),
            client_ip=request.client.host if request.client else None,
        )
        
        # Add request ID to response headers
        response.headers["X-Request-ID"] = request_id
        
        return response


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Middleware for handling errors gracefully"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            return await call_next(request)
        except HTTPException:
            # Let FastAPI handle HTTP exceptions
            raise
        except Exception as e:
            # Log unexpected errors
            request_id = getattr(request.state, "request_id", "unknown")
            logger.error(
                "Unhandled exception in request",
                request_id=request_id,
                path=request.url.path,
                method=request.method,
                error=str(e),
                error_type=type(e).__name__
            )
            
            # Record error metric
            observability_manager.record_error(
                error_type=type(e).__name__,
                endpoint=request.url.path
            )
            
            # Return generic error response
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal Server Error",
                    "message": "An unexpected error occurred",
                    "request_id": request_id,
                    "timestamp": time.time()
                }
            )


class RateLimitingMiddleware(BaseHTTPMiddleware):
    """Middleware for rate limiting requests"""
    
    def __init__(self, app, requests_per_minute: int = 60):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.window_size = 60  # 1 minute window
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip rate limiting for health checks
        if request.url.path.startswith("/health"):
            return await call_next(request)
        
        # Get client identifier (IP address)
        client_ip = request.client.host if request.client else "unknown"
        
        # Create rate limit key
        current_minute = int(time.time()) // self.window_size
        rate_limit_key = CacheKeys.rate_limit(client_ip, str(current_minute))
        
        try:
            # Increment request count
            current_count = await cache_manager.increment(
                rate_limit_key,
                amount=1,
                ttl=self.window_size
            )
            
            # Check if rate limit exceeded
            if current_count > self.requests_per_minute:
                logger.warning(
                    "Rate limit exceeded",
                    client_ip=client_ip,
                    current_count=current_count,
                    limit=self.requests_per_minute
                )
                
                return JSONResponse(
                    status_code=429,
                    content={
                        "error": "Rate Limit Exceeded",
                        "message": f"Too many requests. Limit: {self.requests_per_minute} per minute",
                        "retry_after": self.window_size
                    },
                    headers={"Retry-After": str(self.window_size)}
                )
            
            # Add rate limit headers to response
            response = await call_next(request)
            response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
            response.headers["X-RateLimit-Remaining"] = str(max(0, self.requests_per_minute - current_count))
            response.headers["X-RateLimit-Reset"] = str((current_minute + 1) * self.window_size)
            
            return response
            
        except Exception as e:
            # If rate limiting fails, allow the request through
            logger.error("Rate limiting failed", client_ip=client_ip, error=str(e))
            return await call_next(request)


class MetricsMiddleware(BaseHTTPMiddleware):
    """Middleware for collecting metrics"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Record active connection
        observability_manager.increment_connections()
        
        try:
            # Process request
            response = await call_next(request)
            
            # Record request metric
            observability_manager.record_request(
                endpoint=request.url.path,
                method=request.method,
                status_code=response.status_code
            )
            
            return response
            
        finally:
            # Always decrement connections
            observability_manager.decrement_connections()


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware for adding security headers"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # Add CSP header for non-API endpoints
        if not request.url.path.startswith("/api/"):
            response.headers["Content-Security-Policy"] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "connect-src 'self'"
            )
        
        return response


class CompressionMiddleware(BaseHTTPMiddleware):
    """Middleware for response compression"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Add compression hint for large responses
        if (
            response.headers.get("content-type", "").startswith("application/json") and
            "gzip" in request.headers.get("accept-encoding", "")
        ):
            response.headers["Vary"] = "Accept-Encoding"
        
        return response
