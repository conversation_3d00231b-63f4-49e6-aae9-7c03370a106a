"""
Main FastAPI application for the API Gateway
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from shared.config.settings import settings
from shared.utils.logging import setup_logging, get_logger, request_logger
from shared.utils.observability import setup_observability, observability_manager
from shared.utils.cache import cache_manager
from .middleware import (
    RequestLoggingMiddleware,
    ErrorHandlingMiddleware,
    RateLimitingMiddleware,
    MetricsMiddleware,
)
from .routers import health, tokens, analysis


logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager
    """
    # Startup
    logger.info("Starting Quantum Market Intelligence API Gateway")
    
    # Initialize observability
    setup_observability()
    
    # Test cache connection
    cache_healthy = await cache_manager.health_check()
    if not cache_healthy:
        logger.error("Cache health check failed")
    
    logger.info("API Gateway startup complete", cache_healthy=cache_healthy)
    
    yield
    
    # Shutdown
    logger.info("Shutting down API Gateway")
    await cache_manager.close()
    logger.info("API Gateway shutdown complete")


def create_app() -> FastAPI:
    """
    Create and configure FastAPI application
    """
    app = FastAPI(
        title="Quantum Market Intelligence API",
        description="Production-ready cryptocurrency rug detection and market intelligence system",
        version="1.0.0",
        docs_url="/docs" if settings.app.is_development else None,
        redoc_url="/redoc" if settings.app.is_development else None,
        lifespan=lifespan,
    )
    
    # Add middleware
    setup_middleware(app)
    
    # Add routers
    setup_routers(app)
    
    # Add exception handlers
    setup_exception_handlers(app)
    
    return app


def setup_middleware(app: FastAPI) -> None:
    """Set up application middleware"""
    
    # Security middleware
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"] if settings.app.is_development else ["localhost", "127.0.0.1"]
    )
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.security.cors_origins,
        allow_credentials=settings.security.cors_allow_credentials,
        allow_methods=settings.security.cors_allow_methods,
        allow_headers=settings.security.cors_allow_headers,
    )
    
    # Custom middleware (order matters - last added runs first)
    app.add_middleware(MetricsMiddleware)
    app.add_middleware(ErrorHandlingMiddleware)
    app.add_middleware(RateLimitingMiddleware)
    app.add_middleware(RequestLoggingMiddleware)


def setup_routers(app: FastAPI) -> None:
    """Set up API routers"""
    
    # Health check endpoints
    app.include_router(
        health.router,
        prefix="/health",
        tags=["Health"]
    )
    
    # Token endpoints
    app.include_router(
        tokens.router,
        prefix="/api/v1/tokens",
        tags=["Tokens"]
    )
    
    # Analysis endpoints
    app.include_router(
        analysis.router,
        prefix="/api/v1/analysis",
        tags=["Analysis"]
    )


def setup_exception_handlers(app: FastAPI) -> None:
    """Set up global exception handlers"""
    
    @app.exception_handler(404)
    async def not_found_handler(request: Request, exc):
        return JSONResponse(
            status_code=404,
            content={
                "error": "Not Found",
                "message": f"The requested resource {request.url.path} was not found",
                "timestamp": "2025-01-01T00:00:00Z"
            }
        )
    
    @app.exception_handler(500)
    async def internal_error_handler(request: Request, exc):
        logger.error("Internal server error", path=request.url.path, error=str(exc))
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal Server Error",
                "message": "An unexpected error occurred",
                "timestamp": "2025-01-01T00:00:00Z"
            }
        )


# Create the FastAPI app
app = create_app()


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "name": "Quantum Market Intelligence API",
        "version": "1.0.0",
        "status": "operational",
        "docs": "/docs" if settings.app.is_development else None
    }


@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    # This will be handled by the PrometheusMetricReader
    return Response(content="", media_type="text/plain")


def main():
    """Main entry point for running the application"""
    # Set up logging first
    setup_logging()
    
    logger.info(
        "Starting API Gateway",
        host=settings.app.host,
        port=settings.app.port,
        environment=settings.app.environment,
        debug=settings.app.debug
    )
    
    # Run the application
    uvicorn.run(
        "services.api_gateway.main:app",
        host=settings.app.host,
        port=settings.app.port,
        reload=settings.app.is_development,
        workers=1 if settings.app.is_development else settings.app.workers,
        log_config=None,  # We handle logging ourselves
        access_log=False,  # We handle access logging in middleware
    )


if __name__ == "__main__":
    main()
