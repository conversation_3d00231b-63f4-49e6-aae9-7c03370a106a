"""
Data validation and enrichment pipeline for token data
"""

import re
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from decimal import Decimal

from shared.utils.logging import get_logger
from shared.models.token import ChainType

logger = get_logger(__name__)


class DataValidator:
    """
    Data validation and enrichment pipeline
    
    Features:
    - Contract address validation
    - Chain-specific validation rules
    - Data normalization and enrichment
    - Duplicate detection
    - Quality scoring
    """
    
    def __init__(self):
        self.chain_validators = {
            ChainType.ETHEREUM: self._validate_ethereum_address,
            ChainType.BSC: self._validate_ethereum_address,  # Same format as Ethereum
            ChainType.POLYGON: self._validate_ethereum_address,
            ChainType.ARBITRUM: self._validate_ethereum_address,
            ChainType.OPTIMISM: self._validate_ethereum_address,
            ChainType.BASE: self._validate_ethereum_address,
            ChainType.AVALANCHE: self._validate_ethereum_address,
            "solana": self._validate_solana_address,
        }
    
    def validate_token_data(self, token_data: Dict[str, Any]) -> bool:
        """
        Validate token data structure and content
        
        Args:
            token_data: Token data dictionary
            
        Returns:
            True if valid, False otherwise
        """
        try:
            # Check required fields
            required_fields = ["contract_address", "chain"]
            for field in required_fields:
                if not token_data.get(field):
                    logger.debug(f"Missing required field: {field}")
                    return False
            
            # Validate contract address format
            contract_address = token_data["contract_address"]
            chain = token_data["chain"]
            
            if not self._validate_contract_address(contract_address, chain):
                logger.debug(f"Invalid contract address: {contract_address} for chain: {chain}")
                return False
            
            # Validate chain
            if not self._validate_chain(chain):
                logger.debug(f"Invalid chain: {chain}")
                return False
            
            # Validate optional fields if present
            if "symbol" in token_data:
                if not self._validate_symbol(token_data["symbol"]):
                    logger.debug(f"Invalid symbol: {token_data['symbol']}")
                    return False
            
            if "name" in token_data:
                if not self._validate_name(token_data["name"]):
                    logger.debug(f"Invalid name: {token_data['name']}")
                    return False
            
            if "decimals" in token_data:
                if not self._validate_decimals(token_data["decimals"]):
                    logger.debug(f"Invalid decimals: {token_data['decimals']}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating token data", error=str(e), token_data=token_data)
            return False
    
    def _validate_contract_address(self, address: str, chain: str) -> bool:
        """Validate contract address format for specific chain"""
        if not address or not isinstance(address, str):
            return False
        
        validator = self.chain_validators.get(chain)
        if not validator:
            logger.warning(f"No validator for chain: {chain}")
            return True  # Allow unknown chains for now
        
        return validator(address)
    
    def _validate_ethereum_address(self, address: str) -> bool:
        """Validate Ethereum-style address (42 characters, starts with 0x)"""
        if not address.startswith("0x"):
            return False
        
        if len(address) != 42:
            return False
        
        # Check if it's valid hexadecimal
        try:
            int(address[2:], 16)
            return True
        except ValueError:
            return False
    
    def _validate_solana_address(self, address: str) -> bool:
        """Validate Solana address (base58, 32-44 characters)"""
        if not address or len(address) < 32 or len(address) > 44:
            return False
        
        # Basic base58 character check
        base58_chars = "**********************************************************"
        return all(c in base58_chars for c in address)
    
    def _validate_chain(self, chain: str) -> bool:
        """Validate chain identifier"""
        valid_chains = {
            "ethereum", "bsc", "polygon", "arbitrum", "optimism", 
            "base", "avalanche", "solana"
        }
        return chain.lower() in valid_chains
    
    def _validate_symbol(self, symbol: str) -> bool:
        """Validate token symbol"""
        if not symbol or not isinstance(symbol, str):
            return False
        
        # Symbol should be 1-20 characters, alphanumeric
        if len(symbol) < 1 or len(symbol) > 20:
            return False
        
        # Allow alphanumeric and some special characters
        return re.match(r'^[A-Za-z0-9._-]+$', symbol) is not None
    
    def _validate_name(self, name: str) -> bool:
        """Validate token name"""
        if not name or not isinstance(name, str):
            return False
        
        # Name should be 1-100 characters
        if len(name) < 1 or len(name) > 100:
            return False
        
        # Basic sanitization - no control characters
        return all(ord(c) >= 32 for c in name)
    
    def _validate_decimals(self, decimals: Any) -> bool:
        """Validate token decimals"""
        try:
            decimals_int = int(decimals)
            return 0 <= decimals_int <= 255
        except (ValueError, TypeError):
            return False
    
    def normalize_token_data(self, token_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normalize and enrich token data
        
        Args:
            token_data: Raw token data
            
        Returns:
            Normalized token data
        """
        normalized = token_data.copy()
        
        try:
            # Normalize contract address
            if "contract_address" in normalized:
                normalized["contract_address"] = self._normalize_address(
                    normalized["contract_address"], 
                    normalized.get("chain", "ethereum")
                )
            
            # Normalize chain
            if "chain" in normalized:
                normalized["chain"] = normalized["chain"].lower()
            
            # Normalize symbol
            if "symbol" in normalized and normalized["symbol"]:
                normalized["symbol"] = normalized["symbol"].upper().strip()
            
            # Normalize name
            if "name" in normalized and normalized["name"]:
                normalized["name"] = normalized["name"].strip()
            
            # Normalize decimals
            if "decimals" in normalized:
                try:
                    normalized["decimals"] = int(normalized["decimals"])
                except (ValueError, TypeError):
                    normalized["decimals"] = 18  # Default
            
            # Add validation timestamp
            normalized["validated_at"] = datetime.utcnow().isoformat()
            
            # Calculate quality score
            normalized["quality_score"] = self._calculate_quality_score(normalized)
            
            return normalized
            
        except Exception as e:
            logger.error(f"Error normalizing token data", error=str(e))
            return token_data
    
    def _normalize_address(self, address: str, chain: str) -> str:
        """Normalize address format for specific chain"""
        if not address:
            return address
        
        if chain in ["ethereum", "bsc", "polygon", "arbitrum", "optimism", "base", "avalanche"]:
            # Ethereum-style addresses should be lowercase
            return address.lower()
        elif chain == "solana":
            # Solana addresses are case-sensitive, return as-is
            return address
        else:
            return address.lower()
    
    def _calculate_quality_score(self, token_data: Dict[str, Any]) -> float:
        """
        Calculate quality score for token data (0.0 to 1.0)
        
        Factors:
        - Completeness of data
        - Data validity
        - Source reliability
        - Metadata richness
        """
        score = 0.0
        max_score = 0.0
        
        # Required fields (40% of score)
        required_fields = ["contract_address", "chain"]
        for field in required_fields:
            max_score += 0.2
            if token_data.get(field):
                score += 0.2
        
        # Optional but important fields (30% of score)
        important_fields = ["symbol", "name", "decimals"]
        for field in important_fields:
            max_score += 0.1
            if token_data.get(field):
                score += 0.1
        
        # Metadata richness (20% of score)
        metadata = token_data.get("metadata", {})
        metadata_fields = ["liquidity_usd", "volume_24h_usd", "market_cap", "price_usd"]
        for field in metadata_fields:
            max_score += 0.05
            if metadata.get(field):
                score += 0.05
        
        # Source reliability (10% of score)
        source = token_data.get("source")
        source_scores = {
            "dexscreener": 0.08,
            "coingecko": 0.10,
            "bitquery_pumpfun": 0.09,
            "raydium": 0.07,
            "infura_websocket": 0.06
        }
        max_score += 0.10
        score += source_scores.get(source, 0.05)
        
        return min(1.0, score / max_score) if max_score > 0 else 0.0
    
    def batch_validate(self, tokens: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Batch validate and normalize multiple tokens
        
        Args:
            tokens: List of token data dictionaries
            
        Returns:
            Tuple of (valid_tokens, invalid_tokens)
        """
        valid_tokens = []
        invalid_tokens = []
        
        for token_data in tokens:
            try:
                if self.validate_token_data(token_data):
                    normalized = self.normalize_token_data(token_data)
                    valid_tokens.append(normalized)
                else:
                    invalid_tokens.append(token_data)
            except Exception as e:
                logger.error(f"Error in batch validation", error=str(e))
                invalid_tokens.append(token_data)
        
        logger.info(
            "Batch validation completed",
            total_tokens=len(tokens),
            valid_tokens=len(valid_tokens),
            invalid_tokens=len(invalid_tokens)
        )
        
        return valid_tokens, invalid_tokens
    
    def detect_duplicates(self, tokens: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Detect and remove duplicate tokens based on contract address and chain
        
        Args:
            tokens: List of token data dictionaries
            
        Returns:
            List of unique tokens
        """
        seen = set()
        unique_tokens = []
        
        for token in tokens:
            key = (
                token.get("contract_address", "").lower(),
                token.get("chain", "").lower()
            )
            
            if key not in seen and key[0] and key[1]:
                seen.add(key)
                unique_tokens.append(token)
        
        logger.info(
            "Duplicate detection completed",
            original_count=len(tokens),
            unique_count=len(unique_tokens),
            duplicates_removed=len(tokens) - len(unique_tokens)
        )
        
        return unique_tokens
