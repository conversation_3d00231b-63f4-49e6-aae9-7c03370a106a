"""
Advanced rate limiting with exponential backoff and circuit breakers
"""

import asyncio
import time
from typing import Dict, Optional
from dataclasses import dataclass
from enum import Enum

from shared.utils.logging import get_logger
from shared.utils.cache import cache_manager

logger = get_logger(__name__)


class RateLimitStrategy(str, Enum):
    """Rate limiting strategies"""
    TOKEN_BUCKET = "token_bucket"
    SLIDING_WINDOW = "sliding_window"
    EXPONENTIAL_BACKOFF = "exponential_backoff"


@dataclass
class RateLimitConfig:
    """Rate limit configuration for an API"""
    requests_per_minute: int
    requests_per_second: Optional[int] = None
    burst_limit: Optional[int] = None
    backoff_factor: float = 2.0
    max_backoff_seconds: int = 300
    strategy: RateLimitStrategy = RateLimitStrategy.TOKEN_BUCKET


class APIRateLimiter:
    """
    Advanced rate limiter with multiple strategies and circuit breaker integration
    
    Features:
    - Token bucket algorithm for smooth rate limiting
    - Sliding window for precise control
    - Exponential backoff on failures
    - Per-API configuration
    - Redis-backed distributed rate limiting
    """
    
    def __init__(self):
        self.configs = self._initialize_api_configs()
        self.buckets: Dict[str, Dict] = {}
        self.backoff_states: Dict[str, Dict] = {}
        
        # Initialize buckets for each API
        for api_name in self.configs:
            self._initialize_bucket(api_name)
    
    def _initialize_api_configs(self) -> Dict[str, RateLimitConfig]:
        """Initialize rate limit configurations for different APIs"""
        return {
            "dexscreener": RateLimitConfig(
                requests_per_minute=100,  # Free tier limit
                requests_per_second=2,
                burst_limit=10,
                strategy=RateLimitStrategy.TOKEN_BUCKET
            ),
            "etherscan": RateLimitConfig(
                requests_per_minute=300,  # 5 req/sec * 60
                requests_per_second=5,
                burst_limit=20,
                strategy=RateLimitStrategy.TOKEN_BUCKET
            ),
            "coingecko": RateLimitConfig(
                requests_per_minute=50,  # Free tier
                requests_per_second=1,
                burst_limit=5,
                strategy=RateLimitStrategy.SLIDING_WINDOW
            ),
            "bitquery": RateLimitConfig(
                requests_per_minute=100,
                requests_per_second=2,
                burst_limit=10,
                strategy=RateLimitStrategy.TOKEN_BUCKET
            ),
            "raydium": RateLimitConfig(
                requests_per_minute=120,
                requests_per_second=2,
                burst_limit=15,
                strategy=RateLimitStrategy.TOKEN_BUCKET
            ),
            "birdeye": RateLimitConfig(
                requests_per_minute=60,
                requests_per_second=1,
                burst_limit=5,
                strategy=RateLimitStrategy.SLIDING_WINDOW
            ),
            "infura": RateLimitConfig(
                requests_per_minute=600,  # Higher limit for blockchain RPC
                requests_per_second=10,
                burst_limit=50,
                strategy=RateLimitStrategy.TOKEN_BUCKET
            )
        }
    
    def _initialize_bucket(self, api_name: str):
        """Initialize token bucket for an API"""
        config = self.configs[api_name]
        
        self.buckets[api_name] = {
            "tokens": config.requests_per_minute,
            "capacity": config.requests_per_minute,
            "last_refill": time.time(),
            "refill_rate": config.requests_per_minute / 60.0,  # tokens per second
            "burst_tokens": config.burst_limit or config.requests_per_minute
        }
        
        self.backoff_states[api_name] = {
            "consecutive_failures": 0,
            "last_failure_time": None,
            "current_backoff": 0,
            "next_allowed_time": 0
        }
    
    async def acquire(self, api_name: str, tokens: int = 1) -> bool:
        """
        Acquire tokens from the rate limiter
        
        Args:
            api_name: Name of the API
            tokens: Number of tokens to acquire
            
        Returns:
            True if tokens acquired, False if rate limited
        """
        if api_name not in self.configs:
            logger.warning(f"Unknown API: {api_name}, allowing request")
            return True
        
        config = self.configs[api_name]
        
        # Check exponential backoff
        if not await self._check_backoff(api_name):
            return False
        
        # Apply rate limiting strategy
        if config.strategy == RateLimitStrategy.TOKEN_BUCKET:
            return await self._acquire_token_bucket(api_name, tokens)
        elif config.strategy == RateLimitStrategy.SLIDING_WINDOW:
            return await self._acquire_sliding_window(api_name, tokens)
        else:
            return True
    
    async def _check_backoff(self, api_name: str) -> bool:
        """Check if we're in exponential backoff period"""
        backoff_state = self.backoff_states[api_name]
        current_time = time.time()
        
        if current_time < backoff_state["next_allowed_time"]:
            logger.debug(
                f"API {api_name} in backoff period",
                remaining_seconds=backoff_state["next_allowed_time"] - current_time
            )
            return False
        
        return True
    
    async def _acquire_token_bucket(self, api_name: str, tokens: int) -> bool:
        """Token bucket rate limiting algorithm"""
        bucket = self.buckets[api_name]
        current_time = time.time()
        
        # Refill bucket based on time elapsed
        time_elapsed = current_time - bucket["last_refill"]
        tokens_to_add = time_elapsed * bucket["refill_rate"]
        
        bucket["tokens"] = min(
            bucket["capacity"],
            bucket["tokens"] + tokens_to_add
        )
        bucket["last_refill"] = current_time
        
        # Check if we have enough tokens
        if bucket["tokens"] >= tokens:
            bucket["tokens"] -= tokens
            logger.debug(
                f"Rate limit acquired for {api_name}",
                tokens_used=tokens,
                tokens_remaining=bucket["tokens"]
            )
            return True
        else:
            logger.debug(
                f"Rate limit exceeded for {api_name}",
                tokens_requested=tokens,
                tokens_available=bucket["tokens"]
            )
            return False
    
    async def _acquire_sliding_window(self, api_name: str, tokens: int) -> bool:
        """Sliding window rate limiting using Redis"""
        try:
            current_time = int(time.time())
            window_start = current_time - 60  # 1-minute window
            
            # Use Redis for distributed sliding window
            key = f"rate_limit:{api_name}:{current_time // 60}"
            
            # Get current count in window
            current_count = await cache_manager.get(key) or 0
            config = self.configs[api_name]
            
            if current_count + tokens <= config.requests_per_minute:
                # Increment counter
                await cache_manager.set(key, current_count + tokens, ttl=60)
                logger.debug(
                    f"Sliding window acquired for {api_name}",
                    current_count=current_count + tokens,
                    limit=config.requests_per_minute
                )
                return True
            else:
                logger.debug(
                    f"Sliding window limit exceeded for {api_name}",
                    current_count=current_count,
                    limit=config.requests_per_minute
                )
                return False
                
        except Exception as e:
            logger.error(f"Error in sliding window rate limiter for {api_name}", error=str(e))
            # Fallback to allowing the request
            return True
    
    async def record_success(self, api_name: str):
        """Record successful API call to reset backoff"""
        if api_name in self.backoff_states:
            backoff_state = self.backoff_states[api_name]
            backoff_state["consecutive_failures"] = 0
            backoff_state["current_backoff"] = 0
            backoff_state["next_allowed_time"] = 0
    
    async def record_failure(self, api_name: str, error_type: str = "generic"):
        """Record API failure and apply exponential backoff"""
        if api_name not in self.backoff_states:
            return
        
        config = self.configs[api_name]
        backoff_state = self.backoff_states[api_name]
        
        backoff_state["consecutive_failures"] += 1
        backoff_state["last_failure_time"] = time.time()
        
        # Calculate exponential backoff
        backoff_seconds = min(
            config.backoff_factor ** backoff_state["consecutive_failures"],
            config.max_backoff_seconds
        )
        
        backoff_state["current_backoff"] = backoff_seconds
        backoff_state["next_allowed_time"] = time.time() + backoff_seconds
        
        logger.warning(
            f"API failure recorded for {api_name}",
            consecutive_failures=backoff_state["consecutive_failures"],
            backoff_seconds=backoff_seconds,
            error_type=error_type
        )
    
    async def get_rate_limit_status(self, api_name: str) -> Dict:
        """Get current rate limit status for an API"""
        if api_name not in self.configs:
            return {"error": "Unknown API"}
        
        config = self.configs[api_name]
        bucket = self.buckets.get(api_name, {})
        backoff_state = self.backoff_states.get(api_name, {})
        
        # Refresh bucket state
        if bucket:
            current_time = time.time()
            time_elapsed = current_time - bucket["last_refill"]
            tokens_to_add = time_elapsed * bucket["refill_rate"]
            available_tokens = min(
                bucket["capacity"],
                bucket["tokens"] + tokens_to_add
            )
        else:
            available_tokens = 0
        
        return {
            "api_name": api_name,
            "strategy": config.strategy,
            "requests_per_minute": config.requests_per_minute,
            "available_tokens": available_tokens,
            "capacity": config.requests_per_minute,
            "consecutive_failures": backoff_state.get("consecutive_failures", 0),
            "in_backoff": time.time() < backoff_state.get("next_allowed_time", 0),
            "backoff_remaining": max(0, backoff_state.get("next_allowed_time", 0) - time.time())
        }
    
    async def reset_rate_limits(self, api_name: Optional[str] = None):
        """Reset rate limits for an API or all APIs"""
        if api_name:
            if api_name in self.buckets:
                self._initialize_bucket(api_name)
                logger.info(f"Rate limits reset for {api_name}")
        else:
            for api in self.configs:
                self._initialize_bucket(api)
            logger.info("All rate limits reset")
    
    async def health_check(self) -> Dict:
        """Health check for rate limiter"""
        status = {}
        
        for api_name in self.configs:
            api_status = await self.get_rate_limit_status(api_name)
            status[api_name] = {
                "healthy": not api_status.get("in_backoff", False),
                "available_tokens": api_status.get("available_tokens", 0),
                "consecutive_failures": api_status.get("consecutive_failures", 0)
            }
        
        return {
            "overall_healthy": all(s["healthy"] for s in status.values()),
            "apis": status
        }
