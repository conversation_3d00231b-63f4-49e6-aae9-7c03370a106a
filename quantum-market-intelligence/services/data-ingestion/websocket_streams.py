"""
Real-time WebSocket data streams for multi-chain monitoring
"""

import asyncio
import json
import websockets
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from shared.config.settings import settings
from shared.utils.logging import get_logger
from shared.utils.cache import cache_manager

logger = get_logger(__name__)


class StreamType(str, Enum):
    """Types of WebSocket streams"""
    NEW_CONTRACTS = "new_contracts"
    TOKEN_TRANSFERS = "token_transfers"
    LIQUIDITY_CHANGES = "liquidity_changes"
    PRICE_UPDATES = "price_updates"


@dataclass
class StreamConfig:
    """Configuration for a WebSocket stream"""
    url: str
    subscription_message: Dict[str, Any]
    reconnect_interval: int = 5
    max_reconnect_attempts: int = 10
    heartbeat_interval: int = 30


class WebSocketStreamManager:
    """
    Manager for real-time WebSocket data streams
    
    Features:
    - Multi-chain WebSocket connections
    - Automatic reconnection with exponential backoff
    - Message filtering and routing
    - Health monitoring and alerting
    - Rate limiting and circuit breakers
    """
    
    def __init__(self):
        self.streams: Dict[str, Dict] = {}
        self.handlers: Dict[StreamType, List[Callable]] = {
            StreamType.NEW_CONTRACTS: [],
            StreamType.TOKEN_TRANSFERS: [],
            StreamType.LIQUIDITY_CHANGES: [],
            StreamType.PRICE_UPDATES: []
        }
        self.running = False
        self.stream_configs = self._initialize_stream_configs()
    
    def _initialize_stream_configs(self) -> Dict[str, StreamConfig]:
        """Initialize WebSocket stream configurations"""
        configs = {}
        
        # Infura WebSocket streams for multiple chains
        infura_chains = [
            ("ethereum", "mainnet"),
            ("polygon", "polygon-mainnet"),
            ("arbitrum", "arbitrum-mainnet"),
            ("optimism", "optimism-mainnet"),
            ("base", "base-mainnet"),
            ("avalanche", "avalanche-mainnet")
        ]
        
        for chain_name, infura_network in infura_chains:
            if hasattr(settings.api, f'infura_{infura_network.replace("-", "_")}_ws'):
                ws_url = getattr(settings.api, f'infura_{infura_network.replace("-", "_")}_ws')
                
                configs[f"{chain_name}_new_contracts"] = StreamConfig(
                    url=ws_url,
                    subscription_message={
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "eth_subscribe",
                        "params": [
                            "logs",
                            {
                                "topics": [
                                    "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"  # Transfer event
                                ]
                            }
                        ]
                    }
                )
        
        # Solana WebSocket (if available)
        if hasattr(settings.api, 'solana_ws_url'):
            configs["solana_new_tokens"] = StreamConfig(
                url=settings.api.solana_ws_url,
                subscription_message={
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "programSubscribe",
                    "params": [
                        "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",  # PumpFun program
                        {
                            "encoding": "jsonParsed",
                            "commitment": "confirmed"
                        }
                    ]
                }
            )
        
        return configs
    
    def register_handler(self, stream_type: StreamType, handler: Callable):
        """Register a handler for a specific stream type"""
        self.handlers[stream_type].append(handler)
        logger.info(f"Registered handler for {stream_type}")
    
    async def start_all_streams(self):
        """Start all configured WebSocket streams"""
        self.running = True
        tasks = []
        
        for stream_name, config in self.stream_configs.items():
            task = asyncio.create_task(
                self._manage_stream(stream_name, config),
                name=f"stream_{stream_name}"
            )
            tasks.append(task)
        
        logger.info(f"Started {len(tasks)} WebSocket streams")
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error("Error in stream management", error=str(e))
        finally:
            self.running = False
    
    async def stop_all_streams(self):
        """Stop all WebSocket streams"""
        self.running = False
        logger.info("Stopping all WebSocket streams")
        
        # Cancel all running tasks
        for task in asyncio.all_tasks():
            if task.get_name().startswith("stream_"):
                task.cancel()
    
    async def _manage_stream(self, stream_name: str, config: StreamConfig):
        """Manage a single WebSocket stream with reconnection logic"""
        reconnect_attempts = 0
        
        while self.running and reconnect_attempts < config.max_reconnect_attempts:
            try:
                logger.info(f"Connecting to stream: {stream_name}")
                
                async with websockets.connect(
                    config.url,
                    ping_interval=config.heartbeat_interval,
                    ping_timeout=10,
                    close_timeout=10
                ) as websocket:
                    
                    # Send subscription message
                    await websocket.send(json.dumps(config.subscription_message))
                    logger.info(f"Subscribed to stream: {stream_name}")
                    
                    # Reset reconnect attempts on successful connection
                    reconnect_attempts = 0
                    
                    # Handle messages
                    await self._handle_stream_messages(stream_name, websocket)
                    
            except websockets.exceptions.ConnectionClosed:
                logger.warning(f"Stream {stream_name} connection closed")
            except Exception as e:
                logger.error(f"Error in stream {stream_name}", error=str(e))
            
            if self.running:
                reconnect_attempts += 1
                backoff_time = min(config.reconnect_interval * (2 ** reconnect_attempts), 300)
                logger.info(
                    f"Reconnecting to {stream_name} in {backoff_time}s",
                    attempt=reconnect_attempts
                )
                await asyncio.sleep(backoff_time)
        
        logger.error(f"Max reconnection attempts reached for {stream_name}")
    
    async def _handle_stream_messages(self, stream_name: str, websocket):
        """Handle incoming WebSocket messages"""
        async for message in websocket:
            try:
                data = json.loads(message)
                await self._process_message(stream_name, data)
                
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON from {stream_name}: {message}")
            except Exception as e:
                logger.error(f"Error processing message from {stream_name}", error=str(e))
    
    async def _process_message(self, stream_name: str, data: Dict[str, Any]):
        """Process and route WebSocket messages"""
        try:
            # Determine message type and route to appropriate handlers
            if "new_contracts" in stream_name:
                await self._handle_new_contract(stream_name, data)
            elif "new_tokens" in stream_name:
                await self._handle_new_token(stream_name, data)
            elif "transfers" in stream_name:
                await self._handle_token_transfer(stream_name, data)
            else:
                logger.debug(f"Unhandled message type from {stream_name}")
                
        except Exception as e:
            logger.error(f"Error processing message from {stream_name}", error=str(e))
    
    async def _handle_new_contract(self, stream_name: str, data: Dict[str, Any]):
        """Handle new contract deployment events"""
        try:
            # Extract contract information from Ethereum-style log
            if "params" in data and "result" in data["params"]:
                log_data = data["params"]["result"]
                
                contract_info = {
                    "contract_address": log_data.get("address"),
                    "transaction_hash": log_data.get("transactionHash"),
                    "block_number": log_data.get("blockNumber"),
                    "chain": self._extract_chain_from_stream_name(stream_name),
                    "timestamp": datetime.utcnow().isoformat(),
                    "source": "websocket",
                    "stream_name": stream_name
                }
                
                # Route to handlers
                for handler in self.handlers[StreamType.NEW_CONTRACTS]:
                    try:
                        await handler(contract_info)
                    except Exception as e:
                        logger.error(f"Error in new contract handler", error=str(e))
                
                # Cache for deduplication
                cache_key = f"new_contract:{contract_info['contract_address']}"
                await cache_manager.set(cache_key, contract_info, ttl=3600)
                
        except Exception as e:
            logger.error(f"Error handling new contract from {stream_name}", error=str(e))
    
    async def _handle_new_token(self, stream_name: str, data: Dict[str, Any]):
        """Handle new token creation events (Solana PumpFun)"""
        try:
            # Extract token information from Solana program notification
            if "params" in data and "result" in data["params"]:
                result = data["params"]["result"]
                
                token_info = {
                    "contract_address": result.get("account"),
                    "program_id": result.get("owner"),
                    "chain": "solana",
                    "timestamp": datetime.utcnow().isoformat(),
                    "source": "websocket",
                    "stream_name": stream_name,
                    "raw_data": result
                }
                
                # Route to handlers
                for handler in self.handlers[StreamType.NEW_CONTRACTS]:
                    try:
                        await handler(token_info)
                    except Exception as e:
                        logger.error(f"Error in new token handler", error=str(e))
                
        except Exception as e:
            logger.error(f"Error handling new token from {stream_name}", error=str(e))
    
    async def _handle_token_transfer(self, stream_name: str, data: Dict[str, Any]):
        """Handle token transfer events"""
        try:
            # Extract transfer information
            if "params" in data and "result" in data["params"]:
                log_data = data["params"]["result"]
                
                transfer_info = {
                    "token_address": log_data.get("address"),
                    "transaction_hash": log_data.get("transactionHash"),
                    "block_number": log_data.get("blockNumber"),
                    "chain": self._extract_chain_from_stream_name(stream_name),
                    "timestamp": datetime.utcnow().isoformat(),
                    "topics": log_data.get("topics", []),
                    "data": log_data.get("data"),
                    "source": "websocket"
                }
                
                # Route to handlers
                for handler in self.handlers[StreamType.TOKEN_TRANSFERS]:
                    try:
                        await handler(transfer_info)
                    except Exception as e:
                        logger.error(f"Error in transfer handler", error=str(e))
                
        except Exception as e:
            logger.error(f"Error handling transfer from {stream_name}", error=str(e))
    
    def _extract_chain_from_stream_name(self, stream_name: str) -> str:
        """Extract chain name from stream name"""
        if "ethereum" in stream_name:
            return "ethereum"
        elif "polygon" in stream_name:
            return "polygon"
        elif "arbitrum" in stream_name:
            return "arbitrum"
        elif "optimism" in stream_name:
            return "optimism"
        elif "base" in stream_name:
            return "base"
        elif "avalanche" in stream_name:
            return "avalanche"
        elif "solana" in stream_name:
            return "solana"
        else:
            return "unknown"
    
    async def get_stream_status(self) -> Dict[str, Any]:
        """Get status of all WebSocket streams"""
        status = {
            "running": self.running,
            "total_streams": len(self.stream_configs),
            "streams": {}
        }
        
        for stream_name in self.stream_configs:
            # Check if stream task is running
            stream_running = False
            for task in asyncio.all_tasks():
                if task.get_name() == f"stream_{stream_name}" and not task.done():
                    stream_running = True
                    break
            
            status["streams"][stream_name] = {
                "running": stream_running,
                "handlers_registered": len(self.handlers.get(StreamType.NEW_CONTRACTS, []))
            }
        
        return status
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for WebSocket streams"""
        status = await self.get_stream_status()
        
        healthy_streams = sum(1 for s in status["streams"].values() if s["running"])
        total_streams = len(status["streams"])
        
        return {
            "healthy": self.running and healthy_streams > 0,
            "running_streams": healthy_streams,
            "total_streams": total_streams,
            "health_percentage": (healthy_streams / total_streams * 100) if total_streams > 0 else 0
        }
