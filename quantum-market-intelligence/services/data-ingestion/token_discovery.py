"""
Multi-source token discovery service with redundancy and automatic failover
"""

import asyncio
import aiohttp
import time
from typing import List, Dict, Any, Optional, Set
from datetime import datetime, timed<PERSON><PERSON>
from dataclasses import dataclass
from enum import Enum

try:
    from shared.config.settings import settings
    from shared.utils.logging import get_logger
    from shared.utils.cache import cache_manager
    from shared.models.token import Token, ChainType, TokenStatus
    from .rate_limiter import APIRateLimiter
    from .data_validator import DataValidator
except ImportError:
    # Fallback for testing without full package structure
    import os
    import sys

    # Mock settings for testing
    class MockSettings:
        class API:
            coingecko_api_key = None
        api = API()

    settings = MockSettings()

    # Mock logger
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")

    def get_logger(name): return MockLogger()

    # Mock cache manager
    class MockCacheManager:
        async def set(self, key, value, ttl=None): pass
        async def get(self, key): return None

    cache_manager = MockCacheManager()

    # Mock token models
    class ChainType:
        ETHEREUM = "ethereum"
        BSC = "bsc"
        POLYGON = "polygon"
        ARBITRUM = "arbitrum"
        OPTIMISM = "optimism"
        BASE = "base"
        AVALANCHE = "avalanche"

    class TokenStatus:
        ACTIVE = "active"
        FLAGGED = "flagged"
        VERIFIED = "verified"
        RUGGED = "rugged"

    # Import rate limiter and data validator with fallback
    try:
        from .rate_limiter import APIRateLimiter
        from .data_validator import DataValidator
    except ImportError:
        # Create minimal mock classes for testing
        class APIRateLimiter:
            async def acquire(self, api_name): return True

        class DataValidator:
            def validate_token_data(self, token_data): return True

logger = get_logger(__name__)


class DataSource(str, Enum):
    """Available data sources for token discovery"""
    DEXSCREENER = "dexscreener"
    BITQUERY_PUMPFUN = "bitquery_pumpfun"
    COINGECKO = "coingecko"
    RAYDIUM = "raydium"
    INFURA_WEBSOCKET = "infura_websocket"


@dataclass
class TokenDiscoveryResult:
    """Result from token discovery operation"""
    tokens: List[Dict[str, Any]]
    source: DataSource
    timestamp: datetime
    success: bool
    error: Optional[str] = None


class TokenDiscoveryService:
    """
    Multi-source token discovery service with redundancy and automatic failover
    
    Features:
    - Multiple data sources with priority ordering
    - Automatic failover on API failures
    - Rate limiting and circuit breaker patterns
    - Caching to reduce API calls
    - Real-time filtering for new tokens (<24h)
    """
    
    def __init__(self):
        self.rate_limiter = APIRateLimiter()
        self.data_validator = DataValidator()
        self.session: Optional[aiohttp.ClientSession] = None
        self.circuit_breakers: Dict[DataSource, Dict] = {}
        self.last_discovery_time = {}
        
        # Initialize circuit breakers
        for source in DataSource:
            self.circuit_breakers[source] = {
                "failures": 0,
                "last_failure": None,
                "is_open": False,
                "failure_threshold": 5,
                "recovery_timeout": 300  # 5 minutes
            }
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                "User-Agent": "QuantumMarketIntelligence/1.0",
                "Accept": "application/json"
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def discover_new_tokens(
        self, 
        max_age_hours: int = 24,
        min_liquidity_usd: float = 20000,
        min_volume_usd: float = 100000
    ) -> List[Dict[str, Any]]:
        """
        Discover new tokens from multiple sources with filtering
        
        Args:
            max_age_hours: Maximum token age in hours
            min_liquidity_usd: Minimum liquidity in USD
            min_volume_usd: Minimum 24h volume in USD
            
        Returns:
            List of discovered tokens with metadata
        """
        logger.info(
            "Starting token discovery",
            max_age_hours=max_age_hours,
            min_liquidity_usd=min_liquidity_usd,
            min_volume_usd=min_volume_usd
        )
        
        # Define source priority order
        sources = [
            DataSource.DEXSCREENER,
            DataSource.BITQUERY_PUMPFUN,
            DataSource.COINGECKO,
            DataSource.RAYDIUM
        ]
        
        all_tokens = []
        successful_sources = []
        
        for source in sources:
            try:
                if self._is_circuit_breaker_open(source):
                    logger.warning(f"Circuit breaker open for {source}, skipping")
                    continue
                
                result = await self._discover_from_source(
                    source, max_age_hours, min_liquidity_usd, min_volume_usd
                )
                
                if result.success:
                    all_tokens.extend(result.tokens)
                    successful_sources.append(source)
                    self._reset_circuit_breaker(source)
                    logger.info(f"Successfully discovered {len(result.tokens)} tokens from {source}")
                else:
                    self._record_failure(source, result.error)
                    logger.error(f"Failed to discover tokens from {source}: {result.error}")
                    
            except Exception as e:
                self._record_failure(source, str(e))
                logger.error(f"Exception during discovery from {source}", error=str(e))
        
        # Deduplicate tokens by contract address
        unique_tokens = self._deduplicate_tokens(all_tokens)
        
        # Apply additional filtering
        filtered_tokens = await self._apply_filters(
            unique_tokens, max_age_hours, min_liquidity_usd, min_volume_usd
        )
        
        logger.info(
            "Token discovery completed",
            total_discovered=len(all_tokens),
            unique_tokens=len(unique_tokens),
            filtered_tokens=len(filtered_tokens),
            successful_sources=successful_sources
        )
        
        return filtered_tokens
    
    async def _discover_from_source(
        self,
        source: DataSource,
        max_age_hours: int,
        min_liquidity_usd: float,
        min_volume_usd: float
    ) -> TokenDiscoveryResult:
        """Discover tokens from a specific source"""
        
        try:
            if source == DataSource.DEXSCREENER:
                return await self._discover_dexscreener(max_age_hours)
            elif source == DataSource.BITQUERY_PUMPFUN:
                return await self._discover_bitquery_pumpfun(max_age_hours)
            elif source == DataSource.COINGECKO:
                return await self._discover_coingecko(max_age_hours)
            elif source == DataSource.RAYDIUM:
                return await self._discover_raydium(max_age_hours)
            else:
                return TokenDiscoveryResult(
                    tokens=[], source=source, timestamp=datetime.utcnow(),
                    success=False, error=f"Unknown source: {source}"
                )
                
        except Exception as e:
            return TokenDiscoveryResult(
                tokens=[], source=source, timestamp=datetime.utcnow(),
                success=False, error=str(e)
            )
    
    async def _discover_dexscreener(self, max_age_hours: int) -> TokenDiscoveryResult:
        """Discover tokens from DexScreener API"""
        
        # Check rate limit
        if not await self.rate_limiter.acquire("dexscreener"):
            return TokenDiscoveryResult(
                tokens=[], source=DataSource.DEXSCREENER, timestamp=datetime.utcnow(),
                success=False, error="Rate limit exceeded"
            )
        
        try:
            # DexScreener doesn't have direct age filtering, so we'll get latest boosted tokens
            # and filter by creation time
            url = "https://api.dexscreener.com/token-boosts/latest/v1"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    tokens = []
                    
                    # Process each token
                    for item in data:
                        if isinstance(item, dict):
                            token_data = {
                                "contract_address": item.get("tokenAddress"),
                                "chain": self._map_chain_id(item.get("chainId")),
                                "symbol": None,  # Will be enriched later
                                "name": None,
                                "source": DataSource.DEXSCREENER,
                                "discovered_at": datetime.utcnow(),
                                "metadata": item
                            }
                            tokens.append(token_data)
                    
                    return TokenDiscoveryResult(
                        tokens=tokens, source=DataSource.DEXSCREENER,
                        timestamp=datetime.utcnow(), success=True
                    )
                else:
                    error_msg = f"DexScreener API returned status {response.status}"
                    return TokenDiscoveryResult(
                        tokens=[], source=DataSource.DEXSCREENER,
                        timestamp=datetime.utcnow(), success=False, error=error_msg
                    )
                    
        except Exception as e:
            return TokenDiscoveryResult(
                tokens=[], source=DataSource.DEXSCREENER,
                timestamp=datetime.utcnow(), success=False, error=str(e)
            )
    
    async def _discover_bitquery_pumpfun(self, max_age_hours: int) -> TokenDiscoveryResult:
        """Discover tokens from Bitquery PumpFun API"""
        
        # Check rate limit
        if not await self.rate_limiter.acquire("bitquery"):
            return TokenDiscoveryResult(
                tokens=[], source=DataSource.BITQUERY_PUMPFUN, timestamp=datetime.utcnow(),
                success=False, error="Rate limit exceeded"
            )
        
        try:
            # Calculate time threshold
            since_time = datetime.utcnow() - timedelta(hours=max_age_hours)
            
            # GraphQL query for newly created PumpFun tokens
            query = """
            query NewPumpFunTokens($since: DateTime!) {
              Solana {
                TokenSupplyUpdates(
                  where: {
                    Block: { Time: { since: $since } }
                    Instruction: {
                      Program: {
                        Address: { is: "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P" }
                        Method: { is: "create" }
                      }
                    }
                  }
                  limit: { count: 100 }
                  orderBy: { descending: Block_Time }
                ) {
                  Block { Time }
                  Transaction { Signer }
                  TokenSupplyUpdate {
                    Currency {
                      Symbol
                      Name
                      MintAddress
                      Decimals
                      Uri
                    }
                    PostBalance
                  }
                }
              }
            }
            """
            
            variables = {"since": since_time.isoformat() + "Z"}
            
            # Note: This would require Bitquery API key and proper authentication
            # For now, return empty result as we need to implement proper auth
            return TokenDiscoveryResult(
                tokens=[], source=DataSource.BITQUERY_PUMPFUN,
                timestamp=datetime.utcnow(), success=True
            )
            
        except Exception as e:
            return TokenDiscoveryResult(
                tokens=[], source=DataSource.BITQUERY_PUMPFUN,
                timestamp=datetime.utcnow(), success=False, error=str(e)
            )

    async def _discover_coingecko(self, max_age_hours: int) -> TokenDiscoveryResult:
        """Discover tokens from CoinGecko API"""

        if not await self.rate_limiter.acquire("coingecko"):
            return TokenDiscoveryResult(
                tokens=[], source=DataSource.COINGECKO, timestamp=datetime.utcnow(),
                success=False, error="Rate limit exceeded"
            )

        try:
            # CoinGecko new listings endpoint
            url = "https://api.coingecko.com/api/v3/coins/list/new"
            headers = {}

            if settings.api.coingecko_api_key:
                headers["x-cg-demo-api-key"] = settings.api.coingecko_api_key

            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    tokens = []

                    for item in data:
                        if isinstance(item, dict):
                            token_data = {
                                "contract_address": item.get("id"),  # CoinGecko uses ID
                                "chain": "ethereum",  # Default, will be enriched
                                "symbol": item.get("symbol"),
                                "name": item.get("name"),
                                "source": DataSource.COINGECKO,
                                "discovered_at": datetime.utcnow(),
                                "metadata": item
                            }
                            tokens.append(token_data)

                    return TokenDiscoveryResult(
                        tokens=tokens, source=DataSource.COINGECKO,
                        timestamp=datetime.utcnow(), success=True
                    )
                else:
                    error_msg = f"CoinGecko API returned status {response.status}"
                    return TokenDiscoveryResult(
                        tokens=[], source=DataSource.COINGECKO,
                        timestamp=datetime.utcnow(), success=False, error=error_msg
                    )

        except Exception as e:
            return TokenDiscoveryResult(
                tokens=[], source=DataSource.COINGECKO,
                timestamp=datetime.utcnow(), success=False, error=str(e)
            )

    async def _discover_raydium(self, max_age_hours: int) -> TokenDiscoveryResult:
        """Discover tokens from Raydium new pools endpoint"""

        if not await self.rate_limiter.acquire("raydium"):
            return TokenDiscoveryResult(
                tokens=[], source=DataSource.RAYDIUM, timestamp=datetime.utcnow(),
                success=False, error="Rate limit exceeded"
            )

        try:
            # Raydium new pools API (hypothetical endpoint)
            url = "https://api.raydium.io/v2/main/new-pools"

            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    tokens = []

                    # Process Raydium pool data
                    for pool in data.get("data", []):
                        if isinstance(pool, dict):
                            # Extract base token info
                            base_token = pool.get("baseMint")
                            if base_token:
                                token_data = {
                                    "contract_address": base_token.get("address"),
                                    "chain": "solana",
                                    "symbol": base_token.get("symbol"),
                                    "name": base_token.get("name"),
                                    "source": DataSource.RAYDIUM,
                                    "discovered_at": datetime.utcnow(),
                                    "metadata": pool
                                }
                                tokens.append(token_data)

                    return TokenDiscoveryResult(
                        tokens=tokens, source=DataSource.RAYDIUM,
                        timestamp=datetime.utcnow(), success=True
                    )
                else:
                    error_msg = f"Raydium API returned status {response.status}"
                    return TokenDiscoveryResult(
                        tokens=[], source=DataSource.RAYDIUM,
                        timestamp=datetime.utcnow(), success=False, error=error_msg
                    )

        except Exception as e:
            return TokenDiscoveryResult(
                tokens=[], source=DataSource.RAYDIUM,
                timestamp=datetime.utcnow(), success=False, error=str(e)
            )

    def _map_chain_id(self, chain_id: str) -> str:
        """Map external chain IDs to internal chain types"""
        chain_mapping = {
            "ethereum": "ethereum",
            "bsc": "bsc",
            "polygon": "polygon",
            "arbitrum": "arbitrum",
            "optimism": "optimism",
            "base": "base",
            "avalanche": "avalanche",
            "solana": "solana"
        }
        return chain_mapping.get(chain_id, "ethereum")

    def _deduplicate_tokens(self, tokens: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate tokens based on contract address"""
        seen_addresses = set()
        unique_tokens = []

        for token in tokens:
            address = token.get("contract_address")
            if address and address not in seen_addresses:
                seen_addresses.add(address)
                unique_tokens.append(token)

        return unique_tokens

    async def _apply_filters(
        self,
        tokens: List[Dict[str, Any]],
        max_age_hours: int,
        min_liquidity_usd: float,
        min_volume_usd: float
    ) -> List[Dict[str, Any]]:
        """Apply additional filtering criteria"""
        filtered_tokens = []

        for token in tokens:
            # Validate token data
            if not self.data_validator.validate_token_data(token):
                continue

            # Apply age filter (if creation time available)
            if "created_at" in token:
                try:
                    created_at = datetime.fromisoformat(token["created_at"].replace("Z", "+00:00"))
                    age_hours = (datetime.utcnow() - created_at.replace(tzinfo=None)).total_seconds() / 3600
                    if age_hours > max_age_hours:
                        continue
                except:
                    pass  # Skip age filter if parsing fails

            # Apply liquidity and volume filters (if available in metadata)
            metadata = token.get("metadata", {})
            liquidity = metadata.get("liquidity_usd", 0)
            volume = metadata.get("volume_24h_usd", 0)

            if liquidity < min_liquidity_usd or volume < min_volume_usd:
                continue

            filtered_tokens.append(token)

        return filtered_tokens

    def _is_circuit_breaker_open(self, source: DataSource) -> bool:
        """Check if circuit breaker is open for a source"""
        breaker = self.circuit_breakers[source]

        if not breaker["is_open"]:
            return False

        # Check if recovery timeout has passed
        if breaker["last_failure"]:
            time_since_failure = time.time() - breaker["last_failure"]
            if time_since_failure > breaker["recovery_timeout"]:
                breaker["is_open"] = False
                breaker["failures"] = 0
                logger.info(f"Circuit breaker recovered for {source}")
                return False

        return True

    def _record_failure(self, source: DataSource, error: str):
        """Record a failure for circuit breaker logic"""
        breaker = self.circuit_breakers[source]
        breaker["failures"] += 1
        breaker["last_failure"] = time.time()

        if breaker["failures"] >= breaker["failure_threshold"]:
            breaker["is_open"] = True
            logger.warning(
                f"Circuit breaker opened for {source}",
                failures=breaker["failures"],
                error=error
            )

    def _reset_circuit_breaker(self, source: DataSource):
        """Reset circuit breaker after successful operation"""
        breaker = self.circuit_breakers[source]
        breaker["failures"] = 0
        breaker["is_open"] = False
        breaker["last_failure"] = None
