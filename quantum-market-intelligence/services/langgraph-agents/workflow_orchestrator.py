"""
LangGraph Workflow Orchestrator - Main workflow coordinating all agents with state management
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, TypedDict
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
import statistics

from langgraph.graph import StateGraph, END
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI

try:
    from shared.config.settings import settings
    from shared.utils.logging import get_logger
    from .token_hunter import TokenHunterAgent
    from .contract_auditor import ContractAuditorAgent
    from .onchain_analyst import OnChainAnalystAgent
    from .social_sentiment import SocialSentimentAgent
except ImportError:
    # Mock for testing
    class MockSettings:
        class API:
            openrouter_api_key = "test-key"
        api = API()
    settings = MockSettings()
    
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
    
    def get_logger(name): return MockLogger()

logger = get_logger(__name__)


class AnalysisPhase(str, Enum):
    """Analysis phases in the workflow"""
    DISCOVERY = "discovery"
    AUDIT = "audit"
    ONCHAIN = "onchain"
    SOCIAL = "social"
    SYNTHESIS = "synthesis"
    COMPLETE = "complete"


class RiskLevel(str, Enum):
    """Overall risk levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    MINIMAL = "minimal"


@dataclass
class TokenAnalysisResult:
    """Complete token analysis result"""
    token_address: str
    token_symbol: str
    chain: str
    
    # Individual agent results
    discovery_result: Optional[Dict[str, Any]]
    audit_result: Optional[Dict[str, Any]]
    onchain_result: Optional[Dict[str, Any]]
    social_result: Optional[Dict[str, Any]]
    
    # Synthesized results
    overall_risk_score: float
    risk_level: RiskLevel
    confidence: float
    
    # Risk breakdown
    contract_risk: float
    behavioral_risk: float
    social_risk: float
    
    # Key findings
    critical_issues: List[str]
    warnings: List[str]
    positive_signals: List[str]
    
    # Recommendations
    recommendation: str
    action_items: List[str]
    
    # Metadata
    analysis_duration: float
    analyzed_at: datetime
    agent_versions: Dict[str, str]


class WorkflowState(TypedDict):
    """State for the main workflow"""
    # Input parameters
    token_address: str
    token_symbol: Optional[str]
    chain: str
    analysis_depth: str  # "quick", "standard", "deep"
    
    # Current phase
    current_phase: AnalysisPhase
    
    # Agent results
    discovery_result: Optional[Dict[str, Any]]
    audit_result: Optional[Dict[str, Any]]
    onchain_result: Optional[Dict[str, Any]]
    social_result: Optional[Dict[str, Any]]
    
    # Synthesis
    synthesis_result: Optional[Dict[str, Any]]
    final_result: Optional[TokenAnalysisResult]
    
    # Workflow metadata
    start_time: datetime
    error_messages: List[str]
    agent_timings: Dict[str, float]
    processing_complete: bool


class WorkflowOrchestrator:
    """
    Main LangGraph Workflow Orchestrator
    
    Coordinates all agents in a structured workflow:
    1. Token Discovery & Filtering (Token Hunter)
    2. Contract Static Analysis (Contract Auditor)
    3. On-Chain Behavioral Analysis (On-Chain Analyst)
    4. Social Sentiment Analysis (Social Sentiment)
    5. Synthesis & Risk Scoring (AI-powered)
    """
    
    def __init__(self, openrouter_api_key: Optional[str] = None):
        self.api_key = openrouter_api_key or getattr(settings.api, 'openrouter_api_key', None)
        
        # Initialize LLM for synthesis
        self.llm = ChatOpenAI(
            model="anthropic/claude-3-sonnet-20240229",
            openai_api_key=self.api_key,
            openai_api_base="https://openrouter.ai/api/v1",
            temperature=0.1,
            max_tokens=4000
        ) if self.api_key else None
        
        # Initialize agents
        self.token_hunter = None  # Will be initialized when needed
        self.contract_auditor = None
        self.onchain_analyst = None
        self.social_sentiment = None
        
        self.graph = self._build_graph()
        
        # Risk scoring weights
        self.risk_weights = {
            "contract": 0.4,  # Contract vulnerabilities are critical
            "behavioral": 0.35,  # On-chain behavior is very important
            "social": 0.25  # Social signals provide context
        }
    
    def _build_graph(self) -> StateGraph:
        """Build the main workflow graph"""
        
        workflow = StateGraph(WorkflowState)
        
        # Add nodes for each phase
        workflow.add_node("initialize_analysis", self._initialize_analysis)
        workflow.add_node("discovery_phase", self._discovery_phase)
        workflow.add_node("audit_phase", self._audit_phase)
        workflow.add_node("onchain_phase", self._onchain_phase)
        workflow.add_node("social_phase", self._social_phase)
        workflow.add_node("synthesis_phase", self._synthesis_phase)
        workflow.add_node("finalize_analysis", self._finalize_analysis)
        
        # Define workflow edges
        workflow.set_entry_point("initialize_analysis")
        workflow.add_edge("initialize_analysis", "discovery_phase")
        workflow.add_edge("discovery_phase", "audit_phase")
        workflow.add_edge("audit_phase", "onchain_phase")
        workflow.add_edge("onchain_phase", "social_phase")
        workflow.add_edge("social_phase", "synthesis_phase")
        workflow.add_edge("synthesis_phase", "finalize_analysis")
        workflow.add_edge("finalize_analysis", END)
        
        return workflow.compile()
    
    async def _initialize_analysis(self, state: WorkflowState) -> WorkflowState:
        """Initialize the analysis workflow"""
        logger.info(f"Initializing analysis for {state['token_address']}")
        
        state["current_phase"] = AnalysisPhase.DISCOVERY
        state["start_time"] = datetime.utcnow()
        state["agent_timings"] = {}
        state["error_messages"] = []
        
        # Initialize agents lazily
        if not self.token_hunter:
            self.token_hunter = MockTokenHunterAgent()  # Use mock for testing
        if not self.contract_auditor:
            self.contract_auditor = MockContractAuditorAgent()
        if not self.onchain_analyst:
            self.onchain_analyst = MockOnChainAnalystAgent()
        if not self.social_sentiment:
            self.social_sentiment = MockSocialSentimentAgent()
        
        logger.info("Analysis workflow initialized")
        return state
    
    async def _discovery_phase(self, state: WorkflowState) -> WorkflowState:
        """Run token discovery and initial filtering"""
        logger.info("Running discovery phase")
        
        state["current_phase"] = AnalysisPhase.DISCOVERY
        start_time = datetime.utcnow()
        
        try:
            # For testing, create mock discovery result
            discovery_result = {
                "token_address": state["token_address"],
                "token_symbol": state.get("token_symbol", "UNKNOWN"),
                "chain": state["chain"],
                "basic_info": {
                    "name": "Test Token",
                    "symbol": state.get("token_symbol", "TEST"),
                    "decimals": 18,
                    "total_supply": 1000000
                },
                "market_data": {
                    "price_usd": 0.01,
                    "market_cap": 10000,
                    "liquidity_usd": 50000,
                    "volume_24h": 100000
                },
                "quality_score": 0.7,
                "passed_filters": True,
                "discovery_source": "mock"
            }
            
            state["discovery_result"] = discovery_result
            
            # Extract token symbol if not provided
            if not state.get("token_symbol"):
                state["token_symbol"] = discovery_result["basic_info"]["symbol"]
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            state["agent_timings"]["discovery"] = duration
            
            logger.info(f"Discovery phase completed in {duration:.2f}s")
            
        except Exception as e:
            logger.error(f"Error in discovery phase: {e}")
            state["error_messages"].append(f"Discovery error: {str(e)}")
            state["discovery_result"] = None
        
        return state
    
    async def _audit_phase(self, state: WorkflowState) -> WorkflowState:
        """Run contract audit analysis"""
        logger.info("Running audit phase")
        
        state["current_phase"] = AnalysisPhase.AUDIT
        start_time = datetime.utcnow()
        
        try:
            # Mock audit result
            audit_result = {
                "contract_address": state["token_address"],
                "chain": state["chain"],
                "vulnerabilities": [
                    {
                        "pattern": "ownership_not_renounced",
                        "level": "high",
                        "confidence": 0.9,
                        "description": "Contract ownership has not been renounced",
                        "impact": "Owner can modify contract behavior",
                        "recommendation": "Renounce ownership or use timelock"
                    }
                ],
                "overall_risk_score": 0.6,
                "confidence": 0.8,
                "patterns_checked": 8,
                "source_code_available": True
            }
            
            state["audit_result"] = audit_result
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            state["agent_timings"]["audit"] = duration
            
            logger.info(f"Audit phase completed in {duration:.2f}s")
            
        except Exception as e:
            logger.error(f"Error in audit phase: {e}")
            state["error_messages"].append(f"Audit error: {str(e)}")
            state["audit_result"] = None
        
        return state
    
    async def _onchain_phase(self, state: WorkflowState) -> WorkflowState:
        """Run on-chain behavioral analysis"""
        logger.info("Running on-chain analysis phase")
        
        state["current_phase"] = AnalysisPhase.ONCHAIN
        start_time = datetime.utcnow()
        
        try:
            # Mock on-chain result
            onchain_result = {
                "contract_address": state["token_address"],
                "chain": state["chain"],
                "transaction_patterns": [
                    {
                        "pattern": "whale_accumulation",
                        "risk_level": "high",
                        "confidence": 0.8,
                        "description": "Detected 8 whale transactions totaling $25,000,000",
                        "impact_score": 0.7
                    }
                ],
                "holder_analysis": {
                    "total_holders": 1000,
                    "top_10_concentration": 85.0,
                    "distribution_score": 0.15,
                    "suspicious_patterns": ["Extreme concentration in top 10 holders"]
                },
                "liquidity_analysis": {
                    "total_liquidity_usd": 2500000,
                    "liquidity_locked": True,
                    "stability_score": 0.8
                },
                "overall_risk_score": 0.7,
                "confidence": 0.8
            }
            
            state["onchain_result"] = onchain_result
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            state["agent_timings"]["onchain"] = duration
            
            logger.info(f"On-chain phase completed in {duration:.2f}s")
            
        except Exception as e:
            logger.error(f"Error in on-chain phase: {e}")
            state["error_messages"].append(f"On-chain error: {str(e)}")
            state["onchain_result"] = None
        
        return state
    
    async def _social_phase(self, state: WorkflowState) -> WorkflowState:
        """Run social sentiment analysis"""
        logger.info("Running social sentiment phase")
        
        state["current_phase"] = AnalysisPhase.SOCIAL
        start_time = datetime.utcnow()
        
        try:
            # Mock social result
            social_result = {
                "token_symbol": state["token_symbol"],
                "posts_analyzed": 100,
                "sentiment_analysis": {
                    "overall_sentiment": "positive",
                    "confidence": 0.7,
                    "positive_ratio": 0.6,
                    "negative_ratio": 0.2,
                    "neutral_ratio": 0.2,
                    "emotion_scores": {"fear": 0.1, "greed": 0.4, "excitement": 0.6}
                },
                "influence_analysis": {
                    "total_reach": 150000,
                    "influence_level": "medium",
                    "viral_potential": 0.6
                },
                "suspicious_activities": [
                    {
                        "activity_type": "coordinated_campaign",
                        "suspicion_level": "medium",
                        "confidence": 0.6,
                        "description": "Detected coordinated positive campaign",
                        "impact_score": 0.5
                    }
                ],
                "overall_risk_score": 0.4,
                "confidence": 0.7
            }
            
            state["social_result"] = social_result
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            state["agent_timings"]["social"] = duration
            
            logger.info(f"Social phase completed in {duration:.2f}s")
            
        except Exception as e:
            logger.error(f"Error in social phase: {e}")
            state["error_messages"].append(f"Social error: {str(e)}")
            state["social_result"] = None
        
        return state
    
    async def _synthesis_phase(self, state: WorkflowState) -> WorkflowState:
        """Synthesize all agent results using AI"""
        logger.info("Running synthesis phase")
        
        state["current_phase"] = AnalysisPhase.SYNTHESIS
        start_time = datetime.utcnow()
        
        try:
            # Collect all results
            results = {
                "discovery": state.get("discovery_result"),
                "audit": state.get("audit_result"),
                "onchain": state.get("onchain_result"),
                "social": state.get("social_result")
            }
            
            # Calculate weighted risk scores
            contract_risk = results["audit"]["overall_risk_score"] if results["audit"] else 0.5
            behavioral_risk = results["onchain"]["overall_risk_score"] if results["onchain"] else 0.5
            social_risk = results["social"]["overall_risk_score"] if results["social"] else 0.5
            
            # Calculate overall risk score
            overall_risk_score = (
                contract_risk * self.risk_weights["contract"] +
                behavioral_risk * self.risk_weights["behavioral"] +
                social_risk * self.risk_weights["social"]
            )
            
            # Determine risk level
            if overall_risk_score >= 0.8:
                risk_level = RiskLevel.CRITICAL
            elif overall_risk_score >= 0.6:
                risk_level = RiskLevel.HIGH
            elif overall_risk_score >= 0.4:
                risk_level = RiskLevel.MEDIUM
            elif overall_risk_score >= 0.2:
                risk_level = RiskLevel.LOW
            else:
                risk_level = RiskLevel.MINIMAL
            
            # Extract key findings
            critical_issues = []
            warnings = []
            positive_signals = []
            
            # From audit results
            if results["audit"]:
                for vuln in results["audit"].get("vulnerabilities", []):
                    if vuln["level"] in ["critical", "high"]:
                        critical_issues.append(f"Contract: {vuln['description']}")
                    else:
                        warnings.append(f"Contract: {vuln['description']}")
            
            # From on-chain results
            if results["onchain"]:
                holder_analysis = results["onchain"].get("holder_analysis", {})
                if holder_analysis.get("top_10_concentration", 0) > 80:
                    critical_issues.append("Extreme holder concentration (>80% in top 10)")
                
                for pattern in results["onchain"].get("transaction_patterns", []):
                    if pattern["risk_level"] in ["critical", "high"]:
                        warnings.append(f"On-chain: {pattern['description']}")
            
            # From social results
            if results["social"]:
                for activity in results["social"].get("suspicious_activities", []):
                    if activity["suspicion_level"] in ["critical", "high"]:
                        warnings.append(f"Social: {activity['description']}")
                
                sentiment = results["social"].get("sentiment_analysis", {})
                if sentiment.get("overall_sentiment") == "positive":
                    positive_signals.append("Positive social sentiment detected")
            
            # Generate recommendation
            if risk_level == RiskLevel.CRITICAL:
                recommendation = "AVOID - Critical risks detected. Do not invest."
            elif risk_level == RiskLevel.HIGH:
                recommendation = "HIGH RISK - Proceed with extreme caution. Consider avoiding."
            elif risk_level == RiskLevel.MEDIUM:
                recommendation = "MODERATE RISK - Thorough due diligence required before investing."
            elif risk_level == RiskLevel.LOW:
                recommendation = "LOW RISK - Generally safe but monitor for changes."
            else:
                recommendation = "MINIMAL RISK - Appears safe based on current analysis."
            
            # Calculate confidence
            confidences = []
            for result in results.values():
                if result and "confidence" in result:
                    confidences.append(result["confidence"])
            
            overall_confidence = statistics.mean(confidences) if confidences else 0.5
            
            synthesis_result = {
                "overall_risk_score": overall_risk_score,
                "risk_level": risk_level.value,
                "confidence": overall_confidence,
                "contract_risk": contract_risk,
                "behavioral_risk": behavioral_risk,
                "social_risk": social_risk,
                "critical_issues": critical_issues,
                "warnings": warnings,
                "positive_signals": positive_signals,
                "recommendation": recommendation,
                "risk_breakdown": {
                    "contract": f"{contract_risk:.2f} (weight: {self.risk_weights['contract']})",
                    "behavioral": f"{behavioral_risk:.2f} (weight: {self.risk_weights['behavioral']})",
                    "social": f"{social_risk:.2f} (weight: {self.risk_weights['social']})"
                }
            }
            
            state["synthesis_result"] = synthesis_result
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            state["agent_timings"]["synthesis"] = duration
            
            logger.info(f"Synthesis phase completed in {duration:.2f}s")
            
        except Exception as e:
            logger.error(f"Error in synthesis phase: {e}")
            state["error_messages"].append(f"Synthesis error: {str(e)}")
            state["synthesis_result"] = None
        
        return state
    
    async def _finalize_analysis(self, state: WorkflowState) -> WorkflowState:
        """Finalize the complete analysis"""
        logger.info("Finalizing analysis")
        
        state["current_phase"] = AnalysisPhase.COMPLETE
        
        try:
            synthesis = state.get("synthesis_result", {})
            
            # Create final result
            final_result = TokenAnalysisResult(
                token_address=state["token_address"],
                token_symbol=state.get("token_symbol", "UNKNOWN"),
                chain=state["chain"],
                discovery_result=state.get("discovery_result"),
                audit_result=state.get("audit_result"),
                onchain_result=state.get("onchain_result"),
                social_result=state.get("social_result"),
                overall_risk_score=synthesis.get("overall_risk_score", 0.5),
                risk_level=RiskLevel(synthesis.get("risk_level", "medium")),
                confidence=synthesis.get("confidence", 0.5),
                contract_risk=synthesis.get("contract_risk", 0.5),
                behavioral_risk=synthesis.get("behavioral_risk", 0.5),
                social_risk=synthesis.get("social_risk", 0.5),
                critical_issues=synthesis.get("critical_issues", []),
                warnings=synthesis.get("warnings", []),
                positive_signals=synthesis.get("positive_signals", []),
                recommendation=synthesis.get("recommendation", "Analysis incomplete"),
                action_items=self._generate_action_items(synthesis),
                analysis_duration=(datetime.utcnow() - state["start_time"]).total_seconds(),
                analyzed_at=datetime.utcnow(),
                agent_versions={"workflow": "1.0.0", "mock": "1.0.0"}
            )
            
            state["final_result"] = final_result
            state["processing_complete"] = True
            
            logger.info(f"Analysis completed: {final_result.risk_level} risk, {final_result.overall_risk_score:.2f} score")
            
        except Exception as e:
            logger.error(f"Error finalizing analysis: {e}")
            state["error_messages"].append(f"Finalization error: {str(e)}")
        
        return state
    
    def _generate_action_items(self, synthesis: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations"""
        action_items = []
        
        risk_level = synthesis.get("risk_level", "medium")
        
        if risk_level in ["critical", "high"]:
            action_items.extend([
                "Do not invest until critical issues are resolved",
                "Monitor for contract ownership changes",
                "Check for liquidity lock status"
            ])
        elif risk_level == "medium":
            action_items.extend([
                "Conduct additional due diligence",
                "Monitor holder distribution changes",
                "Set up price alerts for unusual activity"
            ])
        else:
            action_items.extend([
                "Continue monitoring for changes",
                "Consider position sizing based on risk tolerance"
            ])
        
        return action_items
    
    async def analyze_token(
        self,
        token_address: str,
        chain: str = "ethereum",
        token_symbol: Optional[str] = None,
        analysis_depth: str = "standard"
    ) -> Optional[TokenAnalysisResult]:
        """
        Main entry point for complete token analysis
        
        Args:
            token_address: Contract address to analyze
            chain: Blockchain network
            token_symbol: Token symbol (optional, will be discovered)
            analysis_depth: Analysis depth ("quick", "standard", "deep")
            
        Returns:
            Complete token analysis result or None if failed
        """
        
        # Initialize state
        initial_state = WorkflowState(
            token_address=token_address,
            token_symbol=token_symbol,
            chain=chain,
            analysis_depth=analysis_depth,
            current_phase=AnalysisPhase.DISCOVERY,
            discovery_result=None,
            audit_result=None,
            onchain_result=None,
            social_result=None,
            synthesis_result=None,
            final_result=None,
            start_time=datetime.utcnow(),
            error_messages=[],
            agent_timings={},
            processing_complete=False
        )
        
        try:
            final_state = await self.graph.ainvoke(initial_state)
            return final_state.get("final_result")
            
        except Exception as e:
            logger.error(f"Error in workflow execution: {e}")
            return None
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current status of workflow orchestrator"""
        return {
            "orchestrator_type": "langgraph_workflow",
            "llm_available": self.llm is not None,
            "agents_initialized": {
                "token_hunter": self.token_hunter is not None,
                "contract_auditor": self.contract_auditor is not None,
                "onchain_analyst": self.onchain_analyst is not None,
                "social_sentiment": self.social_sentiment is not None
            },
            "risk_weights": self.risk_weights,
            "last_run": datetime.utcnow().isoformat()
        }


# Mock agents for testing
class MockTokenHunterAgent:
    async def hunt_tokens(self): return []

class MockContractAuditorAgent:
    async def audit_contract(self, address, chain): return {}

class MockOnChainAnalystAgent:
    async def analyze_onchain_behavior(self, address, chain): return {}

class MockSocialSentimentAgent:
    async def analyze_social_sentiment(self, symbol): return {}
