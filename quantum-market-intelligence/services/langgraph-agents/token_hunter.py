"""
Token Hunter Agent - Intelligent token discovery with filtering and prioritization
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, TypedDict
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from langgraph.graph import StateGraph, END
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI

try:
    from shared.config.settings import settings
    from shared.utils.logging import get_logger
    from shared.models.token import Token, TokenStatus
    from services.data_ingestion.token_discovery import TokenDiscoveryService
except ImportError:
    # Mock for testing
    class MockSettings:
        class API:
            openrouter_api_key = "test-key"
        api = API()
    settings = MockSettings()
    
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
    
    def get_logger(name): return MockLogger()

logger = get_logger(__name__)


class TokenPriority(str, Enum):
    """Token priority levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    IGNORE = "ignore"


@dataclass
class TokenAnalysis:
    """Token analysis result"""
    token_address: str
    chain: str
    priority: TokenPriority
    risk_score: float
    confidence: float
    reasoning: str
    metadata: Dict[str, Any]
    discovered_at: datetime


class TokenHunterState(TypedDict):
    """State for Token Hunter Agent"""
    raw_tokens: List[Dict[str, Any]]
    filtered_tokens: List[Dict[str, Any]]
    analyzed_tokens: List[TokenAnalysis]
    current_token: Optional[Dict[str, Any]]
    analysis_results: List[Dict[str, Any]]
    error_messages: List[str]
    processing_complete: bool


class TokenHunterAgent:
    """
    Token Hunter Agent using LangGraph for intelligent token discovery
    
    Features:
    - Multi-source token discovery
    - AI-powered filtering and prioritization
    - Risk-based scoring
    - Intelligent reasoning about token potential
    - Integration with OpenRouter for LLM capabilities
    """
    
    def __init__(self, openrouter_api_key: Optional[str] = None):
        self.api_key = openrouter_api_key or getattr(settings.api, 'openrouter_api_key', None)
        
        # Initialize LLM with OpenRouter
        self.llm = ChatOpenAI(
            model="anthropic/claude-3-sonnet-20240229",
            openai_api_key=self.api_key,
            openai_api_base="https://openrouter.ai/api/v1",
            temperature=0.1,
            max_tokens=2000
        ) if self.api_key else None
        
        self.discovery_service = None  # Will be injected
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """Build the LangGraph workflow"""
        
        # Define the graph
        workflow = StateGraph(TokenHunterState)
        
        # Add nodes
        workflow.add_node("discover_tokens", self._discover_tokens)
        workflow.add_node("filter_tokens", self._filter_tokens)
        workflow.add_node("analyze_token", self._analyze_token)
        workflow.add_node("prioritize_tokens", self._prioritize_tokens)
        workflow.add_node("finalize_results", self._finalize_results)
        
        # Define edges
        workflow.set_entry_point("discover_tokens")
        workflow.add_edge("discover_tokens", "filter_tokens")
        workflow.add_edge("filter_tokens", "analyze_token")
        workflow.add_conditional_edges(
            "analyze_token",
            self._should_continue_analysis,
            {
                "continue": "analyze_token",
                "prioritize": "prioritize_tokens"
            }
        )
        workflow.add_edge("prioritize_tokens", "finalize_results")
        workflow.add_edge("finalize_results", END)
        
        return workflow.compile()
    
    async def _discover_tokens(self, state: TokenHunterState) -> TokenHunterState:
        """Discover new tokens from multiple sources"""
        logger.info("Starting token discovery")
        
        try:
            if self.discovery_service:
                # Use real discovery service
                tokens = await self.discovery_service.discover_new_tokens(
                    max_age_hours=24,
                    min_liquidity_usd=20000,
                    min_volume_usd=100000
                )
            else:
                # Mock data for testing
                tokens = [
                    {
                        "contract_address": "******************************************",
                        "chain": "ethereum",
                        "symbol": "TEST1",
                        "name": "Test Token 1",
                        "source": "dexscreener",
                        "metadata": {
                            "liquidity_usd": 50000,
                            "volume_24h_usd": 200000,
                            "price_usd": 0.001,
                            "market_cap": 1000000
                        }
                    },
                    {
                        "contract_address": "******************************************",
                        "chain": "ethereum", 
                        "symbol": "TEST2",
                        "name": "Test Token 2",
                        "source": "coingecko",
                        "metadata": {
                            "liquidity_usd": 30000,
                            "volume_24h_usd": 150000,
                            "price_usd": 0.002,
                            "market_cap": 800000
                        }
                    }
                ]
            
            state["raw_tokens"] = tokens
            logger.info(f"Discovered {len(tokens)} tokens")
            
        except Exception as e:
            logger.error(f"Error in token discovery: {e}")
            state["error_messages"].append(f"Discovery error: {str(e)}")
            state["raw_tokens"] = []
        
        return state
    
    async def _filter_tokens(self, state: TokenHunterState) -> TokenHunterState:
        """Apply initial filtering to discovered tokens"""
        logger.info("Filtering discovered tokens")
        
        filtered_tokens = []
        
        for token in state["raw_tokens"]:
            try:
                # Basic validation
                if not token.get("contract_address") or not token.get("chain"):
                    continue
                
                # Metadata-based filtering
                metadata = token.get("metadata", {})
                liquidity = metadata.get("liquidity_usd", 0)
                volume = metadata.get("volume_24h_usd", 0)
                
                # Apply thresholds
                if liquidity >= 20000 and volume >= 100000:
                    # Additional quality checks
                    if self._passes_quality_checks(token):
                        filtered_tokens.append(token)
                        
            except Exception as e:
                logger.error(f"Error filtering token {token.get('contract_address')}: {e}")
                continue
        
        state["filtered_tokens"] = filtered_tokens
        logger.info(f"Filtered to {len(filtered_tokens)} tokens")
        
        return state
    
    def _passes_quality_checks(self, token: Dict[str, Any]) -> bool:
        """Apply quality checks to token"""
        
        # Check for suspicious patterns
        symbol = token.get("symbol", "").upper()
        name = token.get("name", "").lower()
        
        # Red flags
        suspicious_keywords = [
            "scam", "rug", "test", "fake", "ponzi", "pyramid",
            "moon", "safe", "baby", "doge", "shib", "inu"
        ]
        
        for keyword in suspicious_keywords:
            if keyword in symbol.lower() or keyword in name:
                return False
        
        # Check for reasonable symbol length
        if len(symbol) > 20 or len(symbol) < 2:
            return False
        
        # Check for reasonable name length
        if len(name) > 100 or len(name) < 3:
            return False
        
        return True
    
    async def _analyze_token(self, state: TokenHunterState) -> TokenHunterState:
        """Analyze individual token using AI"""
        
        # Get next token to analyze
        if not state.get("current_token"):
            if state["filtered_tokens"]:
                state["current_token"] = state["filtered_tokens"].pop(0)
            else:
                state["processing_complete"] = True
                return state
        
        token = state["current_token"]
        logger.info(f"Analyzing token: {token.get('symbol')} ({token.get('contract_address')})")
        
        try:
            if self.llm:
                analysis = await self._ai_analyze_token(token)
            else:
                # Fallback analysis without AI
                analysis = self._basic_analyze_token(token)
            
            state["analyzed_tokens"].append(analysis)
            state["current_token"] = None  # Clear current token
            
        except Exception as e:
            logger.error(f"Error analyzing token: {e}")
            state["error_messages"].append(f"Analysis error: {str(e)}")
            state["current_token"] = None
        
        return state
    
    async def _ai_analyze_token(self, token: Dict[str, Any]) -> TokenAnalysis:
        """Use AI to analyze token"""
        
        # Prepare context for AI analysis
        context = self._prepare_token_context(token)
        
        system_prompt = """You are an expert cryptocurrency analyst specializing in identifying potential rug pulls and scam tokens. 

Analyze the provided token data and provide:
1. Risk assessment (0.0 to 1.0, where 1.0 is highest risk)
2. Priority level (critical/high/medium/low/ignore)
3. Confidence in your assessment (0.0 to 1.0)
4. Detailed reasoning for your assessment

Consider factors like:
- Token name and symbol patterns
- Liquidity and volume metrics
- Market cap and price
- Source reliability
- Suspicious patterns or red flags

Respond in JSON format with keys: risk_score, priority, confidence, reasoning"""
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f"Analyze this token:\n{json.dumps(context, indent=2)}")
        ]
        
        try:
            response = await self.llm.ainvoke(messages)
            result = json.loads(response.content)
            
            return TokenAnalysis(
                token_address=token["contract_address"],
                chain=token["chain"],
                priority=TokenPriority(result.get("priority", "medium")),
                risk_score=float(result.get("risk_score", 0.5)),
                confidence=float(result.get("confidence", 0.5)),
                reasoning=result.get("reasoning", "AI analysis completed"),
                metadata=token.get("metadata", {}),
                discovered_at=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"AI analysis failed: {e}")
            return self._basic_analyze_token(token)
    
    def _basic_analyze_token(self, token: Dict[str, Any]) -> TokenAnalysis:
        """Basic token analysis without AI"""
        
        metadata = token.get("metadata", {})
        liquidity = metadata.get("liquidity_usd", 0)
        volume = metadata.get("volume_24h_usd", 0)
        market_cap = metadata.get("market_cap", 0)
        
        # Simple scoring based on metrics
        risk_score = 0.5  # Default medium risk
        
        # Adjust based on liquidity
        if liquidity < 50000:
            risk_score += 0.2
        elif liquidity > 500000:
            risk_score -= 0.1
        
        # Adjust based on volume
        if volume < 200000:
            risk_score += 0.1
        elif volume > 1000000:
            risk_score -= 0.1
        
        # Adjust based on market cap
        if market_cap < 1000000:
            risk_score += 0.1
        elif market_cap > 10000000:
            risk_score -= 0.1
        
        risk_score = max(0.0, min(1.0, risk_score))
        
        # Determine priority
        if risk_score > 0.8:
            priority = TokenPriority.CRITICAL
        elif risk_score > 0.6:
            priority = TokenPriority.HIGH
        elif risk_score > 0.4:
            priority = TokenPriority.MEDIUM
        else:
            priority = TokenPriority.LOW
        
        return TokenAnalysis(
            token_address=token["contract_address"],
            chain=token["chain"],
            priority=priority,
            risk_score=risk_score,
            confidence=0.7,  # Medium confidence for basic analysis
            reasoning=f"Basic analysis: liquidity=${liquidity:,.0f}, volume=${volume:,.0f}, risk_score={risk_score:.2f}",
            metadata=metadata,
            discovered_at=datetime.utcnow()
        )
    
    def _prepare_token_context(self, token: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare token context for AI analysis"""
        return {
            "contract_address": token.get("contract_address"),
            "chain": token.get("chain"),
            "symbol": token.get("symbol"),
            "name": token.get("name"),
            "source": token.get("source"),
            "metadata": token.get("metadata", {}),
            "discovered_at": datetime.utcnow().isoformat()
        }
    
    def _should_continue_analysis(self, state: TokenHunterState) -> str:
        """Determine if we should continue analyzing tokens"""
        if state.get("processing_complete") or not state["filtered_tokens"]:
            return "prioritize"
        return "continue"
    
    async def _prioritize_tokens(self, state: TokenHunterState) -> TokenHunterState:
        """Prioritize analyzed tokens"""
        logger.info("Prioritizing analyzed tokens")
        
        # Sort by priority and risk score
        priority_order = {
            TokenPriority.CRITICAL: 0,
            TokenPriority.HIGH: 1,
            TokenPriority.MEDIUM: 2,
            TokenPriority.LOW: 3,
            TokenPriority.IGNORE: 4
        }
        
        sorted_tokens = sorted(
            state["analyzed_tokens"],
            key=lambda x: (priority_order[x.priority], -x.risk_score, -x.confidence)
        )
        
        state["analyzed_tokens"] = sorted_tokens
        logger.info(f"Prioritized {len(sorted_tokens)} tokens")
        
        return state
    
    async def _finalize_results(self, state: TokenHunterState) -> TokenHunterState:
        """Finalize analysis results"""
        logger.info("Finalizing token hunter results")
        
        results = []
        for analysis in state["analyzed_tokens"]:
            results.append({
                "token_address": analysis.token_address,
                "chain": analysis.chain,
                "priority": analysis.priority.value,
                "risk_score": analysis.risk_score,
                "confidence": analysis.confidence,
                "reasoning": analysis.reasoning,
                "metadata": analysis.metadata,
                "discovered_at": analysis.discovered_at.isoformat()
            })
        
        state["analysis_results"] = results
        state["processing_complete"] = True
        
        logger.info(f"Token hunter completed analysis of {len(results)} tokens")
        return state
    
    async def hunt_tokens(
        self,
        discovery_service: Optional[Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Main entry point for token hunting
        
        Args:
            discovery_service: Token discovery service instance
            
        Returns:
            List of analyzed and prioritized tokens
        """
        
        if discovery_service:
            self.discovery_service = discovery_service
        
        # Initialize state
        initial_state = TokenHunterState(
            raw_tokens=[],
            filtered_tokens=[],
            analyzed_tokens=[],
            current_token=None,
            analysis_results=[],
            error_messages=[],
            processing_complete=False
        )
        
        # Run the workflow
        try:
            final_state = await self.graph.ainvoke(initial_state)
            return final_state["analysis_results"]
            
        except Exception as e:
            logger.error(f"Error in token hunting workflow: {e}")
            return []
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current status of token hunter"""
        return {
            "agent_type": "token_hunter",
            "llm_available": self.llm is not None,
            "discovery_service_available": self.discovery_service is not None,
            "last_run": datetime.utcnow().isoformat()
        }
