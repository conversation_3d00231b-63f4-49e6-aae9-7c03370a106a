"""
On-Chain Analyst Agent - Behavioral analysis for transaction patterns, holder distribution, and liquidity tracking
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, TypedDict
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import statistics

from langgraph.graph import StateGraph, END
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI

try:
    from shared.config.settings import settings
    from shared.utils.logging import get_logger
except ImportError:
    # Mock for testing
    class MockSettings:
        class API:
            openrouter_api_key = "test-key"
            etherscan_api_key = "test-key"
            infura_api_key = "test-key"
        api = API()
    settings = MockSettings()
    
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
    
    def get_logger(name): return MockLogger()

logger = get_logger(__name__)


class BehaviorPattern(str, Enum):
    """On-chain behavior patterns"""
    WHALE_ACCUMULATION = "whale_accumulation"
    INSIDER_TRADING = "insider_trading"
    BOT_ACTIVITY = "bot_activity"
    WASH_TRADING = "wash_trading"
    LIQUIDITY_MANIPULATION = "liquidity_manipulation"
    PUMP_AND_DUMP = "pump_and_dump"
    COORDINATED_SELLING = "coordinated_selling"
    SNIPING = "sniping"


class RiskLevel(str, Enum):
    """Risk levels for behavioral patterns"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class TransactionPattern:
    """Transaction pattern analysis result"""
    pattern: BehaviorPattern
    risk_level: RiskLevel
    confidence: float
    description: str
    evidence: List[str]
    impact_score: float
    recommendation: str


@dataclass
class HolderAnalysis:
    """Holder distribution analysis"""
    total_holders: int
    top_10_concentration: float
    top_50_concentration: float
    whale_addresses: List[str]
    suspicious_patterns: List[str]
    distribution_score: float  # 0-1, higher is more decentralized


@dataclass
class LiquidityAnalysis:
    """Liquidity analysis result"""
    total_liquidity_usd: float
    liquidity_locked: bool
    lock_duration: Optional[int]
    major_pools: List[Dict[str, Any]]
    liquidity_changes: List[Dict[str, Any]]
    stability_score: float  # 0-1, higher is more stable


@dataclass
class OnChainAnalysisResult:
    """Complete on-chain analysis result"""
    contract_address: str
    chain: str
    transaction_patterns: List[TransactionPattern]
    holder_analysis: HolderAnalysis
    liquidity_analysis: LiquidityAnalysis
    overall_risk_score: float
    confidence: float
    analyzed_at: datetime


class OnChainAnalystState(TypedDict):
    """State for On-Chain Analyst Agent"""
    contract_address: str
    chain: str
    transactions: List[Dict[str, Any]]
    holders: List[Dict[str, Any]]
    liquidity_data: Dict[str, Any]
    transaction_patterns: List[TransactionPattern]
    holder_analysis: Optional[HolderAnalysis]
    liquidity_analysis: Optional[LiquidityAnalysis]
    analysis_result: Optional[OnChainAnalysisResult]
    error_messages: List[str]
    processing_complete: bool


class OnChainAnalystAgent:
    """
    On-Chain Analyst Agent using LangGraph for behavioral analysis
    
    Features:
    - Transaction pattern analysis
    - Holder distribution analysis
    - Liquidity tracking and analysis
    - Bot activity detection
    - Whale movement tracking
    - Risk scoring based on on-chain behavior
    """
    
    def __init__(self, openrouter_api_key: Optional[str] = None):
        self.api_key = openrouter_api_key or getattr(settings.api, 'openrouter_api_key', None)
        
        # Initialize LLM with OpenRouter
        self.llm = ChatOpenAI(
            model="anthropic/claude-3-sonnet-20240229",
            openai_api_key=self.api_key,
            openai_api_base="https://openrouter.ai/api/v1",
            temperature=0.1,
            max_tokens=3000
        ) if self.api_key else None
        
        self.etherscan_api_key = getattr(settings.api, 'etherscan_api_key', None)
        self.infura_api_key = getattr(settings.api, 'infura_api_key', None)
        self.graph = self._build_graph()
        
        # Pattern detection thresholds
        self.thresholds = {
            "whale_threshold": 1000000,  # $1M+ transactions
            "bot_time_window": 60,  # seconds
            "concentration_threshold": 0.5,  # 50% concentration
            "liquidity_change_threshold": 0.2  # 20% change
        }
    
    def _build_graph(self) -> StateGraph:
        """Build the LangGraph workflow"""
        
        workflow = StateGraph(OnChainAnalystState)
        
        # Add nodes
        workflow.add_node("fetch_transaction_data", self._fetch_transaction_data)
        workflow.add_node("fetch_holder_data", self._fetch_holder_data)
        workflow.add_node("fetch_liquidity_data", self._fetch_liquidity_data)
        workflow.add_node("analyze_transaction_patterns", self._analyze_transaction_patterns)
        workflow.add_node("analyze_holder_distribution", self._analyze_holder_distribution)
        workflow.add_node("analyze_liquidity", self._analyze_liquidity)
        workflow.add_node("calculate_overall_risk", self._calculate_overall_risk)
        workflow.add_node("finalize_analysis", self._finalize_analysis)
        
        # Define edges
        workflow.set_entry_point("fetch_transaction_data")
        workflow.add_edge("fetch_transaction_data", "fetch_holder_data")
        workflow.add_edge("fetch_holder_data", "fetch_liquidity_data")
        workflow.add_edge("fetch_liquidity_data", "analyze_transaction_patterns")
        workflow.add_edge("analyze_transaction_patterns", "analyze_holder_distribution")
        workflow.add_edge("analyze_holder_distribution", "analyze_liquidity")
        workflow.add_edge("analyze_liquidity", "calculate_overall_risk")
        workflow.add_edge("calculate_overall_risk", "finalize_analysis")
        workflow.add_edge("finalize_analysis", END)
        
        return workflow.compile()
    
    async def _fetch_transaction_data(self, state: OnChainAnalystState) -> OnChainAnalystState:
        """Fetch recent transaction data"""
        logger.info(f"Fetching transaction data for {state['contract_address']}")
        
        try:
            # For testing, generate mock transaction data
            state["transactions"] = self._generate_mock_transactions()
            logger.info(f"Fetched {len(state['transactions'])} transactions")
            
        except Exception as e:
            logger.error(f"Error fetching transaction data: {e}")
            state["error_messages"].append(f"Transaction fetch error: {str(e)}")
            state["transactions"] = []
        
        return state
    
    def _generate_mock_transactions(self) -> List[Dict[str, Any]]:
        """Generate mock transaction data for testing"""
        import random
        
        transactions = []
        base_time = datetime.utcnow()
        
        # Generate various transaction patterns
        for i in range(100):
            tx_time = base_time - timedelta(minutes=random.randint(1, 1440))  # Last 24 hours
            
            # Create different types of transactions
            if i < 10:  # Whale transactions
                tx = {
                    "hash": f"0x{''.join(random.choices('0123456789abcdef', k=64))}",
                    "from": f"0x{''.join(random.choices('0123456789abcdef', k=40))}",
                    "to": f"0x{''.join(random.choices('0123456789abcdef', k=40))}",
                    "value": random.randint(1000000, 10000000),  # $1M-$10M
                    "timestamp": tx_time.isoformat(),
                    "gas_used": random.randint(50000, 200000),
                    "gas_price": random.randint(20, 100),
                    "type": "whale"
                }
            elif i < 30:  # Bot-like transactions
                tx = {
                    "hash": f"0x{''.join(random.choices('0123456789abcdef', k=64))}",
                    "from": f"0x{''.join(random.choices('0123456789abcdef', k=40))}",
                    "to": f"0x{''.join(random.choices('0123456789abcdef', k=40))}",
                    "value": random.randint(1000, 10000),
                    "timestamp": (base_time - timedelta(seconds=i*30)).isoformat(),  # Regular intervals
                    "gas_used": 21000,  # Exact gas usage
                    "gas_price": 50,  # Same gas price
                    "type": "bot"
                }
            else:  # Normal transactions
                tx = {
                    "hash": f"0x{''.join(random.choices('0123456789abcdef', k=64))}",
                    "from": f"0x{''.join(random.choices('0123456789abcdef', k=40))}",
                    "to": f"0x{''.join(random.choices('0123456789abcdef', k=40))}",
                    "value": random.randint(100, 50000),
                    "timestamp": tx_time.isoformat(),
                    "gas_used": random.randint(21000, 100000),
                    "gas_price": random.randint(20, 100),
                    "type": "normal"
                }
            
            transactions.append(tx)
        
        return transactions
    
    async def _fetch_holder_data(self, state: OnChainAnalystState) -> OnChainAnalystState:
        """Fetch token holder data"""
        logger.info("Fetching holder data")
        
        try:
            # Generate mock holder data
            state["holders"] = self._generate_mock_holders()
            logger.info(f"Fetched {len(state['holders'])} holders")
            
        except Exception as e:
            logger.error(f"Error fetching holder data: {e}")
            state["error_messages"].append(f"Holder fetch error: {str(e)}")
            state["holders"] = []
        
        return state
    
    def _generate_mock_holders(self) -> List[Dict[str, Any]]:
        """Generate mock holder data"""
        import random
        
        holders = []
        total_supply = 1000000
        
        # Generate holder distribution with concentration
        for i in range(1000):
            if i < 10:  # Top 10 holders (whales)
                balance = random.randint(50000, 200000)  # 5-20% each
            elif i < 100:  # Next 90 holders
                balance = random.randint(1000, 10000)
            else:  # Remaining holders
                balance = random.randint(1, 1000)
            
            holder = {
                "address": f"0x{''.join(random.choices('0123456789abcdef', k=40))}",
                "balance": balance,
                "percentage": (balance / total_supply) * 100,
                "first_tx": datetime.utcnow() - timedelta(days=random.randint(1, 30)),
                "tx_count": random.randint(1, 100)
            }
            holders.append(holder)
        
        # Sort by balance descending
        holders.sort(key=lambda x: x["balance"], reverse=True)
        
        return holders
    
    async def _fetch_liquidity_data(self, state: OnChainAnalystState) -> OnChainAnalystState:
        """Fetch liquidity data"""
        logger.info("Fetching liquidity data")
        
        try:
            # Generate mock liquidity data
            state["liquidity_data"] = self._generate_mock_liquidity()
            logger.info("Liquidity data fetched")
            
        except Exception as e:
            logger.error(f"Error fetching liquidity data: {e}")
            state["error_messages"].append(f"Liquidity fetch error: {str(e)}")
            state["liquidity_data"] = {}
        
        return state
    
    def _generate_mock_liquidity(self) -> Dict[str, Any]:
        """Generate mock liquidity data"""
        import random
        
        return {
            "total_liquidity_usd": random.randint(100000, 5000000),
            "locked": random.choice([True, False]),
            "lock_duration": random.randint(30, 365) if random.choice([True, False]) else None,
            "pools": [
                {
                    "dex": "Uniswap V2",
                    "pair": "TOKEN/ETH",
                    "liquidity_usd": random.randint(50000, 2000000),
                    "volume_24h": random.randint(100000, 1000000)
                },
                {
                    "dex": "Uniswap V3",
                    "pair": "TOKEN/USDC",
                    "liquidity_usd": random.randint(30000, 1500000),
                    "volume_24h": random.randint(50000, 800000)
                }
            ],
            "changes_24h": [
                {"time": "2024-01-01T10:00:00", "change": -0.15},
                {"time": "2024-01-01T14:00:00", "change": 0.25},
                {"time": "2024-01-01T18:00:00", "change": -0.10}
            ]
        }
    
    async def _analyze_transaction_patterns(self, state: OnChainAnalystState) -> OnChainAnalystState:
        """Analyze transaction patterns for suspicious behavior"""
        logger.info("Analyzing transaction patterns")
        
        patterns = []
        transactions = state["transactions"]
        
        try:
            # Detect whale activity
            whale_pattern = self._detect_whale_activity(transactions)
            if whale_pattern:
                patterns.append(whale_pattern)
            
            # Detect bot activity
            bot_pattern = self._detect_bot_activity(transactions)
            if bot_pattern:
                patterns.append(bot_pattern)
            
            # Detect wash trading
            wash_pattern = self._detect_wash_trading(transactions)
            if wash_pattern:
                patterns.append(wash_pattern)
            
            # Detect sniping
            snipe_pattern = self._detect_sniping(transactions)
            if snipe_pattern:
                patterns.append(snipe_pattern)
            
            state["transaction_patterns"] = patterns
            logger.info(f"Detected {len(patterns)} transaction patterns")
            
        except Exception as e:
            logger.error(f"Error analyzing transaction patterns: {e}")
            state["error_messages"].append(f"Pattern analysis error: {str(e)}")
            state["transaction_patterns"] = []
        
        return state
    
    def _detect_whale_activity(self, transactions: List[Dict[str, Any]]) -> Optional[TransactionPattern]:
        """Detect whale activity patterns"""
        whale_txs = [tx for tx in transactions if tx.get("value", 0) > self.thresholds["whale_threshold"]]
        
        if len(whale_txs) > 5:  # Significant whale activity
            total_value = sum(tx["value"] for tx in whale_txs)
            
            return TransactionPattern(
                pattern=BehaviorPattern.WHALE_ACCUMULATION,
                risk_level=RiskLevel.HIGH,
                confidence=0.8,
                description=f"Detected {len(whale_txs)} whale transactions totaling ${total_value:,.0f}",
                evidence=[f"Transaction {tx['hash'][:10]}... - ${tx['value']:,.0f}" for tx in whale_txs[:3]],
                impact_score=0.7,
                recommendation="Monitor whale addresses for coordinated activity"
            )
        
        return None
    
    def _detect_bot_activity(self, transactions: List[Dict[str, Any]]) -> Optional[TransactionPattern]:
        """Detect bot trading patterns"""
        bot_txs = [tx for tx in transactions if tx.get("type") == "bot"]
        
        if len(bot_txs) > 10:
            # Check for regular intervals
            timestamps = [datetime.fromisoformat(tx["timestamp"]) for tx in bot_txs]
            timestamps.sort()
            
            intervals = [(timestamps[i+1] - timestamps[i]).total_seconds() for i in range(len(timestamps)-1)]
            avg_interval = statistics.mean(intervals) if intervals else 0
            
            if avg_interval < 120:  # Less than 2 minutes average
                return TransactionPattern(
                    pattern=BehaviorPattern.BOT_ACTIVITY,
                    risk_level=RiskLevel.MEDIUM,
                    confidence=0.9,
                    description=f"Detected {len(bot_txs)} bot transactions with {avg_interval:.1f}s average interval",
                    evidence=[f"Regular intervals: {avg_interval:.1f}s", "Identical gas usage", "Same gas price"],
                    impact_score=0.5,
                    recommendation="Investigate for market manipulation"
                )
        
        return None
    
    def _detect_wash_trading(self, transactions: List[Dict[str, Any]]) -> Optional[TransactionPattern]:
        """Detect wash trading patterns"""
        # Group transactions by address pairs
        address_pairs = {}
        
        for tx in transactions:
            pair = tuple(sorted([tx["from"], tx["to"]]))
            if pair not in address_pairs:
                address_pairs[pair] = []
            address_pairs[pair].append(tx)
        
        # Look for back-and-forth trading
        suspicious_pairs = []
        for pair, txs in address_pairs.items():
            if len(txs) > 5:  # Multiple transactions between same addresses
                suspicious_pairs.append((pair, len(txs)))
        
        if suspicious_pairs:
            return TransactionPattern(
                pattern=BehaviorPattern.WASH_TRADING,
                risk_level=RiskLevel.HIGH,
                confidence=0.7,
                description=f"Detected {len(suspicious_pairs)} address pairs with repeated trading",
                evidence=[f"Pair {pair[0][:10]}.../{pair[1][:10]}... - {count} transactions" 
                         for pair, count in suspicious_pairs[:3]],
                impact_score=0.8,
                recommendation="Investigate for artificial volume inflation"
            )
        
        return None
    
    def _detect_sniping(self, transactions: List[Dict[str, Any]]) -> Optional[TransactionPattern]:
        """Detect sniping activity (early large purchases)"""
        # Sort transactions by timestamp
        sorted_txs = sorted(transactions, key=lambda x: x["timestamp"])
        
        # Check first 10 transactions for large purchases
        early_txs = sorted_txs[:10]
        large_early_txs = [tx for tx in early_txs if tx.get("value", 0) > 100000]  # $100k+
        
        if len(large_early_txs) > 2:
            return TransactionPattern(
                pattern=BehaviorPattern.SNIPING,
                risk_level=RiskLevel.MEDIUM,
                confidence=0.6,
                description=f"Detected {len(large_early_txs)} large purchases in first 10 transactions",
                evidence=[f"Early large purchase: ${tx['value']:,.0f}" for tx in large_early_txs],
                impact_score=0.4,
                recommendation="Monitor for insider trading patterns"
            )
        
        return None

    async def _analyze_holder_distribution(self, state: OnChainAnalystState) -> OnChainAnalystState:
        """Analyze holder distribution patterns"""
        logger.info("Analyzing holder distribution")

        try:
            holders = state["holders"]

            if not holders:
                state["holder_analysis"] = None
                return state

            # Calculate concentration metrics
            total_supply = sum(h["balance"] for h in holders)
            top_10_balance = sum(h["balance"] for h in holders[:10])
            top_50_balance = sum(h["balance"] for h in holders[:50])

            top_10_concentration = (top_10_balance / total_supply) * 100
            top_50_concentration = (top_50_balance / total_supply) * 100

            # Identify whale addresses (>1% of supply)
            whale_threshold = total_supply * 0.01
            whale_addresses = [h["address"] for h in holders if h["balance"] > whale_threshold]

            # Detect suspicious patterns
            suspicious_patterns = []

            if top_10_concentration > 70:
                suspicious_patterns.append("Extreme concentration in top 10 holders")

            if len(whale_addresses) < 5:
                suspicious_patterns.append("Very few whale addresses")

            # Check for similar balance patterns (potential sybil)
            balance_groups = {}
            for holder in holders:
                balance_range = (holder["balance"] // 1000) * 1000  # Group by 1000s
                if balance_range not in balance_groups:
                    balance_groups[balance_range] = 0
                balance_groups[balance_range] += 1

            for balance_range, count in balance_groups.items():
                if count > 20 and balance_range > 0:  # Many holders with similar balance
                    suspicious_patterns.append(f"{count} holders with similar balance (~{balance_range:,})")

            # Calculate distribution score (0-1, higher is better)
            distribution_score = 1.0 - (top_10_concentration / 100)
            distribution_score = max(0.0, min(1.0, distribution_score))

            state["holder_analysis"] = HolderAnalysis(
                total_holders=len(holders),
                top_10_concentration=top_10_concentration,
                top_50_concentration=top_50_concentration,
                whale_addresses=whale_addresses,
                suspicious_patterns=suspicious_patterns,
                distribution_score=distribution_score
            )

            logger.info(f"Holder analysis completed: {len(holders)} holders, {top_10_concentration:.1f}% top-10 concentration")

        except Exception as e:
            logger.error(f"Error analyzing holder distribution: {e}")
            state["error_messages"].append(f"Holder analysis error: {str(e)}")
            state["holder_analysis"] = None

        return state

    async def _analyze_liquidity(self, state: OnChainAnalystState) -> OnChainAnalystState:
        """Analyze liquidity patterns"""
        logger.info("Analyzing liquidity")

        try:
            liquidity_data = state["liquidity_data"]

            if not liquidity_data:
                state["liquidity_analysis"] = None
                return state

            # Extract liquidity metrics
            total_liquidity = liquidity_data.get("total_liquidity_usd", 0)
            is_locked = liquidity_data.get("locked", False)
            lock_duration = liquidity_data.get("lock_duration")
            pools = liquidity_data.get("pools", [])
            changes = liquidity_data.get("changes_24h", [])

            # Calculate stability score based on changes
            if changes:
                change_values = [abs(change["change"]) for change in changes]
                avg_volatility = statistics.mean(change_values)
                stability_score = max(0.0, 1.0 - (avg_volatility * 2))  # Scale volatility
            else:
                stability_score = 0.5  # Neutral if no data

            # Adjust stability score based on lock status
            if is_locked and lock_duration and lock_duration > 90:  # 3+ months
                stability_score = min(1.0, stability_score + 0.3)
            elif is_locked:
                stability_score = min(1.0, stability_score + 0.1)

            state["liquidity_analysis"] = LiquidityAnalysis(
                total_liquidity_usd=total_liquidity,
                liquidity_locked=is_locked,
                lock_duration=lock_duration,
                major_pools=pools,
                liquidity_changes=changes,
                stability_score=stability_score
            )

            logger.info(f"Liquidity analysis completed: ${total_liquidity:,.0f} total, locked={is_locked}")

        except Exception as e:
            logger.error(f"Error analyzing liquidity: {e}")
            state["error_messages"].append(f"Liquidity analysis error: {str(e)}")
            state["liquidity_analysis"] = None

        return state

    async def _calculate_overall_risk(self, state: OnChainAnalystState) -> OnChainAnalystState:
        """Calculate overall risk score"""
        logger.info("Calculating overall risk score")

        try:
            risk_factors = []
            confidence_factors = []

            # Transaction pattern risks
            for pattern in state["transaction_patterns"]:
                risk_weight = {
                    RiskLevel.CRITICAL: 1.0,
                    RiskLevel.HIGH: 0.7,
                    RiskLevel.MEDIUM: 0.4,
                    RiskLevel.LOW: 0.2
                }[pattern.risk_level]

                risk_factors.append(risk_weight * pattern.confidence)
                confidence_factors.append(pattern.confidence)

            # Holder distribution risk
            if state["holder_analysis"]:
                holder_risk = 1.0 - state["holder_analysis"].distribution_score
                risk_factors.append(holder_risk * 0.8)  # High confidence in holder data
                confidence_factors.append(0.8)

            # Liquidity risk
            if state["liquidity_analysis"]:
                liquidity_risk = 1.0 - state["liquidity_analysis"].stability_score
                risk_factors.append(liquidity_risk * 0.7)
                confidence_factors.append(0.7)

            # Calculate overall scores
            if risk_factors:
                overall_risk = statistics.mean(risk_factors)
                overall_confidence = statistics.mean(confidence_factors)
            else:
                overall_risk = 0.5  # Neutral if no data
                overall_confidence = 0.3  # Low confidence

            state["overall_risk_score"] = min(1.0, overall_risk)
            state["overall_confidence"] = min(1.0, overall_confidence)

            logger.info(f"Overall risk calculated: {overall_risk:.2f} (confidence: {overall_confidence:.2f})")

        except Exception as e:
            logger.error(f"Error calculating overall risk: {e}")
            state["error_messages"].append(f"Risk calculation error: {str(e)}")
            state["overall_risk_score"] = 0.5
            state["overall_confidence"] = 0.3

        return state

    async def _finalize_analysis(self, state: OnChainAnalystState) -> OnChainAnalystState:
        """Finalize on-chain analysis"""
        logger.info("Finalizing on-chain analysis")

        try:
            analysis_result = OnChainAnalysisResult(
                contract_address=state["contract_address"],
                chain=state["chain"],
                transaction_patterns=state["transaction_patterns"],
                holder_analysis=state["holder_analysis"],
                liquidity_analysis=state["liquidity_analysis"],
                overall_risk_score=state.get("overall_risk_score", 0.5),
                confidence=state.get("overall_confidence", 0.5),
                analyzed_at=datetime.utcnow()
            )

            state["analysis_result"] = analysis_result
            state["processing_complete"] = True

            logger.info(f"On-chain analysis completed for {state['contract_address']}")

        except Exception as e:
            logger.error(f"Error finalizing analysis: {e}")
            state["error_messages"].append(f"Finalization error: {str(e)}")

        return state

    async def analyze_onchain_behavior(
        self,
        contract_address: str,
        chain: str = "ethereum"
    ) -> Optional[OnChainAnalysisResult]:
        """
        Main entry point for on-chain behavioral analysis

        Args:
            contract_address: Contract address to analyze
            chain: Blockchain network

        Returns:
            On-chain analysis result or None if failed
        """

        # Initialize state
        initial_state = OnChainAnalystState(
            contract_address=contract_address,
            chain=chain,
            transactions=[],
            holders=[],
            liquidity_data={},
            transaction_patterns=[],
            holder_analysis=None,
            liquidity_analysis=None,
            analysis_result=None,
            error_messages=[],
            processing_complete=False
        )

        try:
            final_state = await self.graph.ainvoke(initial_state)
            return final_state.get("analysis_result")

        except Exception as e:
            logger.error(f"Error in on-chain analysis workflow: {e}")
            return None

    async def get_status(self) -> Dict[str, Any]:
        """Get current status of on-chain analyst"""
        return {
            "agent_type": "onchain_analyst",
            "llm_available": self.llm is not None,
            "etherscan_available": self.etherscan_api_key is not None,
            "infura_available": self.infura_api_key is not None,
            "thresholds": self.thresholds,
            "last_run": datetime.utcnow().isoformat()
        }
