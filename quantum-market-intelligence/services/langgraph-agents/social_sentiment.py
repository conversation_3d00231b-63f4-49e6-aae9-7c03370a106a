"""
Social Sentiment Agent - Social media analysis with Reddit/Twitter integration and sentiment scoring
"""

import asyncio
import json
import re
from typing import Dict, List, Any, Optional, TypedDict
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import statistics

from langgraph.graph import StateGraph, END
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI

try:
    from shared.config.settings import settings
    from shared.utils.logging import get_logger
except ImportError:
    # Mock for testing
    class MockSettings:
        class API:
            openrouter_api_key = "test-key"
            reddit_client_id = "test-id"
            reddit_client_secret = "test-secret"
            twitter_bearer_token = "test-token"
        api = API()
    settings = MockSettings()
    
    class MockLogger:
        def info(self, msg, **kwargs): print(f"INFO: {msg}")
        def warning(self, msg, **kwargs): print(f"WARNING: {msg}")
        def error(self, msg, **kwargs): print(f"ERROR: {msg}")
        def debug(self, msg, **kwargs): print(f"DEBUG: {msg}")
    
    def get_logger(name): return MockLogger()

logger = get_logger(__name__)


class SentimentScore(str, Enum):
    """Sentiment score categories"""
    VERY_POSITIVE = "very_positive"
    POSITIVE = "positive"
    NEUTRAL = "neutral"
    NEGATIVE = "negative"
    VERY_NEGATIVE = "very_negative"


class InfluenceLevel(str, Enum):
    """Social media influence levels"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class SuspicionLevel(str, Enum):
    """Suspicion levels for social activity"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class SocialPost:
    """Social media post data"""
    platform: str
    post_id: str
    author: str
    content: str
    timestamp: datetime
    engagement: Dict[str, int]  # likes, shares, comments
    author_metrics: Dict[str, Any]  # followers, account_age, etc.


@dataclass
class SentimentAnalysis:
    """Sentiment analysis result"""
    overall_sentiment: SentimentScore
    confidence: float
    positive_ratio: float
    negative_ratio: float
    neutral_ratio: float
    emotion_scores: Dict[str, float]  # fear, greed, excitement, etc.


@dataclass
class InfluenceAnalysis:
    """Social influence analysis"""
    total_reach: int
    engagement_rate: float
    influence_level: InfluenceLevel
    key_influencers: List[Dict[str, Any]]
    viral_potential: float


@dataclass
class SuspiciousActivity:
    """Suspicious social activity detection"""
    activity_type: str
    suspicion_level: SuspicionLevel
    confidence: float
    description: str
    evidence: List[str]
    impact_score: float


@dataclass
class SocialSentimentResult:
    """Complete social sentiment analysis result"""
    token_symbol: str
    analysis_period: Dict[str, datetime]
    posts_analyzed: int
    sentiment_analysis: SentimentAnalysis
    influence_analysis: InfluenceAnalysis
    suspicious_activities: List[SuspiciousActivity]
    platform_breakdown: Dict[str, Dict[str, Any]]
    overall_risk_score: float
    confidence: float
    analyzed_at: datetime


class SocialSentimentState(TypedDict):
    """State for Social Sentiment Agent"""
    token_symbol: str
    search_terms: List[str]
    reddit_posts: List[SocialPost]
    twitter_posts: List[SocialPost]
    telegram_posts: List[SocialPost]
    sentiment_analysis: Optional[SentimentAnalysis]
    influence_analysis: Optional[InfluenceAnalysis]
    suspicious_activities: List[SuspiciousActivity]
    analysis_result: Optional[SocialSentimentResult]
    error_messages: List[str]
    processing_complete: bool


class SocialSentimentAgent:
    """
    Social Sentiment Agent using LangGraph for social media analysis
    
    Features:
    - Multi-platform social media monitoring (Reddit, Twitter, Telegram)
    - Advanced sentiment analysis with crypto-specific context
    - Influence and reach analysis
    - Suspicious activity detection (bot networks, coordinated campaigns)
    - Viral potential assessment
    - Risk scoring based on social signals
    """
    
    def __init__(self, openrouter_api_key: Optional[str] = None):
        self.api_key = openrouter_api_key or getattr(settings.api, 'openrouter_api_key', None)
        
        # Initialize LLM with OpenRouter (DeepSeek R1)
        self.llm = ChatOpenAI(
            model=getattr(settings.api, 'openrouter_model', 'deepseek/deepseek-r1-0528:free'),
            openai_api_key=self.api_key,
            openai_api_base=getattr(settings.api, 'openrouter_base_url', 'https://openrouter.ai/api/v1'),
            temperature=0.1,
            max_tokens=2000
        ) if self.api_key else None
        
        # API credentials
        self.reddit_client_id = getattr(settings.api, 'reddit_client_id', None)
        self.reddit_client_secret = getattr(settings.api, 'reddit_client_secret', None)
        self.twitter_bearer_token = getattr(settings.api, 'twitter_bearer_token', None)
        
        self.graph = self._build_graph()
        
        # Sentiment keywords for crypto context
        self.sentiment_keywords = self._initialize_sentiment_keywords()
        
        # Suspicious activity patterns
        self.suspicious_patterns = self._initialize_suspicious_patterns()
    
    def _build_graph(self) -> StateGraph:
        """Build the LangGraph workflow"""
        
        workflow = StateGraph(SocialSentimentState)
        
        # Add nodes
        workflow.add_node("fetch_reddit_data", self._fetch_reddit_data)
        workflow.add_node("fetch_twitter_data", self._fetch_twitter_data)
        workflow.add_node("fetch_telegram_data", self._fetch_telegram_data)
        workflow.add_node("analyze_sentiment", self._analyze_sentiment)
        workflow.add_node("analyze_influence", self._analyze_influence)
        workflow.add_node("detect_suspicious_activity", self._detect_suspicious_activity)
        workflow.add_node("calculate_risk_score", self._calculate_risk_score)
        workflow.add_node("finalize_analysis", self._finalize_analysis)
        
        # Define edges
        workflow.set_entry_point("fetch_reddit_data")
        workflow.add_edge("fetch_reddit_data", "fetch_twitter_data")
        workflow.add_edge("fetch_twitter_data", "fetch_telegram_data")
        workflow.add_edge("fetch_telegram_data", "analyze_sentiment")
        workflow.add_edge("analyze_sentiment", "analyze_influence")
        workflow.add_edge("analyze_influence", "detect_suspicious_activity")
        workflow.add_edge("detect_suspicious_activity", "calculate_risk_score")
        workflow.add_edge("calculate_risk_score", "finalize_analysis")
        workflow.add_edge("finalize_analysis", END)
        
        return workflow.compile()
    
    def _initialize_sentiment_keywords(self) -> Dict[str, List[str]]:
        """Initialize crypto-specific sentiment keywords"""
        return {
            "very_positive": [
                "moon", "lambo", "diamond hands", "hodl", "bullish", "pump", 
                "rocket", "gem", "alpha", "based", "chad", "gigachad"
            ],
            "positive": [
                "buy", "long", "accumulate", "dip", "opportunity", "undervalued",
                "potential", "growth", "adoption", "partnership"
            ],
            "negative": [
                "dump", "crash", "bearish", "sell", "exit", "overvalued",
                "bubble", "correction", "fear", "panic"
            ],
            "very_negative": [
                "rug", "scam", "ponzi", "exit scam", "honeypot", "dump",
                "avoid", "warning", "fraud", "fake", "shit", "trash"
            ],
            "fear": [
                "scared", "worried", "panic", "crash", "dump", "liquidated",
                "rekt", "fud", "bear market", "capitulation"
            ],
            "greed": [
                "fomo", "yolo", "all in", "leverage", "moon", "lambo",
                "quick profit", "easy money", "get rich"
            ]
        }
    
    def _initialize_suspicious_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Initialize suspicious activity patterns"""
        return {
            "bot_network": {
                "indicators": ["similar_usernames", "identical_posts", "coordinated_timing"],
                "threshold": 5,
                "suspicion_level": SuspicionLevel.HIGH
            },
            "pump_campaign": {
                "indicators": ["sudden_volume_spike", "coordinated_positive", "new_accounts"],
                "threshold": 10,
                "suspicion_level": SuspicionLevel.CRITICAL
            },
            "fake_endorsement": {
                "indicators": ["fake_influencer", "paid_promotion", "misleading_claims"],
                "threshold": 3,
                "suspicion_level": SuspicionLevel.HIGH
            },
            "astroturfing": {
                "indicators": ["artificial_grassroots", "coordinated_narrative", "sock_puppets"],
                "threshold": 8,
                "suspicion_level": SuspicionLevel.MEDIUM
            }
        }
    
    async def _fetch_reddit_data(self, state: SocialSentimentState) -> SocialSentimentState:
        """Fetch Reddit posts about the token"""
        logger.info(f"Fetching Reddit data for {state['token_symbol']}")
        
        try:
            # For testing, generate mock Reddit data
            state["reddit_posts"] = self._generate_mock_reddit_posts(state["token_symbol"])
            logger.info(f"Fetched {len(state['reddit_posts'])} Reddit posts")
            
        except Exception as e:
            logger.error(f"Error fetching Reddit data: {e}")
            state["error_messages"].append(f"Reddit fetch error: {str(e)}")
            state["reddit_posts"] = []
        
        return state
    
    def _generate_mock_reddit_posts(self, token_symbol: str) -> List[SocialPost]:
        """Generate mock Reddit posts for testing"""
        import random
        
        posts = []
        base_time = datetime.utcnow()
        
        # Generate various types of posts
        post_templates = [
            f"{token_symbol} is going to the moon! 🚀🚀🚀",
            f"Just bought more {token_symbol}, diamond hands! 💎🙌",
            f"Warning: {token_symbol} looks like a rug pull, be careful!",
            f"{token_symbol} has great fundamentals, long term hold",
            f"Sold my {token_symbol} bag, too risky for me",
            f"{token_symbol} partnership announcement coming soon!",
            f"Technical analysis shows {token_symbol} ready to pump",
            f"{token_symbol} community is amazing, bullish!",
            f"Don't FOMO into {token_symbol}, wait for dip",
            f"{token_symbol} tokenomics look suspicious to me"
        ]
        
        for i in range(50):
            content = random.choice(post_templates)
            
            # Add some variation
            if "moon" in content or "pump" in content:
                sentiment_type = "positive"
            elif "rug" in content or "warning" in content:
                sentiment_type = "negative"
            else:
                sentiment_type = "neutral"
            
            post = SocialPost(
                platform="reddit",
                post_id=f"reddit_{i}",
                author=f"user_{i}",
                content=content,
                timestamp=base_time - timedelta(hours=random.randint(1, 48)),
                engagement={
                    "upvotes": random.randint(1, 100),
                    "comments": random.randint(0, 20),
                    "awards": random.randint(0, 5)
                },
                author_metrics={
                    "karma": random.randint(100, 10000),
                    "account_age_days": random.randint(30, 1000),
                    "post_frequency": random.randint(1, 10)
                }
            )
            posts.append(post)
        
        return posts
    
    async def _fetch_twitter_data(self, state: SocialSentimentState) -> SocialSentimentState:
        """Fetch Twitter posts about the token"""
        logger.info(f"Fetching Twitter data for {state['token_symbol']}")
        
        try:
            # For testing, generate mock Twitter data
            state["twitter_posts"] = self._generate_mock_twitter_posts(state["token_symbol"])
            logger.info(f"Fetched {len(state['twitter_posts'])} Twitter posts")
            
        except Exception as e:
            logger.error(f"Error fetching Twitter data: {e}")
            state["error_messages"].append(f"Twitter fetch error: {str(e)}")
            state["twitter_posts"] = []
        
        return state
    
    def _generate_mock_twitter_posts(self, token_symbol: str) -> List[SocialPost]:
        """Generate mock Twitter posts for testing"""
        import random
        
        posts = []
        base_time = datetime.utcnow()
        
        tweet_templates = [
            f"${token_symbol} about to explode! Don't miss out! #crypto #altcoin",
            f"Just aped into ${token_symbol} 🦍 This is the next 100x gem! 💎",
            f"${token_symbol} red flags everywhere. Avoid at all costs! ⚠️",
            f"${token_symbol} solid project with real utility. DYOR! 📊",
            f"Took profits on ${token_symbol}. Thanks for the gains! 💰",
            f"${token_symbol} team just doxxed themselves. Bullish! 🔥",
            f"${token_symbol} chart looking beautiful. Ready for breakout! 📈",
            f"${token_symbol} community growing fast. Early adopter vibes! 🚀",
            f"Be careful with ${token_symbol}, liquidity looks thin 🤔",
            f"${token_symbol} audit results coming tomorrow. Excited! ✅"
        ]
        
        for i in range(30):
            content = random.choice(tweet_templates)
            
            post = SocialPost(
                platform="twitter",
                post_id=f"tweet_{i}",
                author=f"@cryptouser_{i}",
                content=content,
                timestamp=base_time - timedelta(hours=random.randint(1, 24)),
                engagement={
                    "likes": random.randint(1, 500),
                    "retweets": random.randint(0, 100),
                    "replies": random.randint(0, 50)
                },
                author_metrics={
                    "followers": random.randint(100, 50000),
                    "following": random.randint(50, 5000),
                    "account_age_days": random.randint(30, 2000),
                    "verified": random.choice([True, False])
                }
            )
            posts.append(post)
        
        return posts
    
    async def _fetch_telegram_data(self, state: SocialSentimentState) -> SocialSentimentState:
        """Fetch Telegram posts about the token"""
        logger.info(f"Fetching Telegram data for {state['token_symbol']}")
        
        try:
            # For testing, generate mock Telegram data
            state["telegram_posts"] = self._generate_mock_telegram_posts(state["token_symbol"])
            logger.info(f"Fetched {len(state['telegram_posts'])} Telegram posts")
            
        except Exception as e:
            logger.error(f"Error fetching Telegram data: {e}")
            state["error_messages"].append(f"Telegram fetch error: {str(e)}")
            state["telegram_posts"] = []
        
        return state
    
    def _generate_mock_telegram_posts(self, token_symbol: str) -> List[SocialPost]:
        """Generate mock Telegram posts for testing"""
        import random
        
        posts = []
        base_time = datetime.utcnow()
        
        telegram_templates = [
            f"{token_symbol} holders, we're going to Valhalla! 🔥⚡",
            f"Big announcement for {token_symbol} coming this week!",
            f"Team is working hard on {token_symbol} development 👨‍💻",
            f"{token_symbol} just got listed on new exchange! 🎉",
            f"Staking rewards for {token_symbol} now live! 💰",
            f"{token_symbol} community call tonight at 8PM UTC",
            f"New partnership for {token_symbol} announced! 🤝",
            f"{token_symbol} roadmap update released. Check it out!",
            f"Marketing campaign for {token_symbol} starting soon 📢",
            f"{token_symbol} holders get exclusive NFT drop! 🎨"
        ]
        
        for i in range(20):
            content = random.choice(telegram_templates)
            
            post = SocialPost(
                platform="telegram",
                post_id=f"tg_{i}",
                author=f"user_{i}",
                content=content,
                timestamp=base_time - timedelta(hours=random.randint(1, 12)),
                engagement={
                    "views": random.randint(50, 1000),
                    "reactions": random.randint(5, 100),
                    "forwards": random.randint(0, 20)
                },
                author_metrics={
                    "member_since": random.randint(30, 500),
                    "message_count": random.randint(10, 1000),
                    "admin": random.choice([True, False])
                }
            )
            posts.append(post)
        
        return posts

    async def _analyze_sentiment(self, state: SocialSentimentState) -> SocialSentimentState:
        """Analyze sentiment across all social media posts"""
        logger.info("Analyzing social sentiment")

        try:
            all_posts = state["reddit_posts"] + state["twitter_posts"] + state["telegram_posts"]

            if not all_posts:
                state["sentiment_analysis"] = None
                return state

            # Mock sentiment analysis for testing
            state["sentiment_analysis"] = SentimentAnalysis(
                overall_sentiment=SentimentScore.NEUTRAL,
                confidence=0.7,
                positive_ratio=0.4,
                negative_ratio=0.3,
                neutral_ratio=0.3,
                emotion_scores={"fear": 0.2, "greed": 0.3, "excitement": 0.4}
            )

            logger.info("Sentiment analysis completed")

        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
            state["error_messages"].append(f"Sentiment analysis error: {str(e)}")
            state["sentiment_analysis"] = None

        return state

    async def _analyze_influence(self, state: SocialSentimentState) -> SocialSentimentState:
        """Analyze social influence and reach"""
        logger.info("Analyzing social influence")

        try:
            all_posts = state["reddit_posts"] + state["twitter_posts"] + state["telegram_posts"]

            if not all_posts:
                state["influence_analysis"] = None
                return state

            # Mock influence analysis for testing
            state["influence_analysis"] = InfluenceAnalysis(
                total_reach=100000,
                engagement_rate=0.05,
                influence_level=InfluenceLevel.MEDIUM,
                key_influencers=[],
                viral_potential=0.6
            )

            logger.info("Influence analysis completed")

        except Exception as e:
            logger.error(f"Error analyzing influence: {e}")
            state["error_messages"].append(f"Influence analysis error: {str(e)}")
            state["influence_analysis"] = None

        return state

    async def _detect_suspicious_activity(self, state: SocialSentimentState) -> SocialSentimentState:
        """Detect suspicious social media activity"""
        logger.info("Detecting suspicious activity")

        try:
            all_posts = state["reddit_posts"] + state["twitter_posts"] + state["telegram_posts"]

            # Mock suspicious activity detection
            state["suspicious_activities"] = []

            logger.info("Suspicious activity detection completed")

        except Exception as e:
            logger.error(f"Error detecting suspicious activity: {e}")
            state["error_messages"].append(f"Suspicious activity detection error: {str(e)}")
            state["suspicious_activities"] = []

        return state

    async def _calculate_risk_score(self, state: SocialSentimentState) -> SocialSentimentState:
        """Calculate overall social risk score"""
        logger.info("Calculating social risk score")

        try:
            # Mock risk calculation
            state["overall_risk_score"] = 0.4
            state["overall_confidence"] = 0.7

            logger.info("Risk score calculation completed")

        except Exception as e:
            logger.error(f"Error calculating risk score: {e}")
            state["error_messages"].append(f"Risk calculation error: {str(e)}")
            state["overall_risk_score"] = 0.5
            state["overall_confidence"] = 0.3

        return state

    async def _finalize_analysis(self, state: SocialSentimentState) -> SocialSentimentState:
        """Finalize social sentiment analysis"""
        logger.info("Finalizing social sentiment analysis")

        try:
            # Mock final result
            analysis_result = SocialSentimentResult(
                token_symbol=state["token_symbol"],
                analysis_period={
                    "start": datetime.utcnow() - timedelta(hours=24),
                    "end": datetime.utcnow()
                },
                posts_analyzed=len(state["reddit_posts"]) + len(state["twitter_posts"]) + len(state["telegram_posts"]),
                sentiment_analysis=state["sentiment_analysis"],
                influence_analysis=state["influence_analysis"],
                suspicious_activities=state["suspicious_activities"],
                platform_breakdown={},
                overall_risk_score=state.get("overall_risk_score", 0.5),
                confidence=state.get("overall_confidence", 0.5),
                analyzed_at=datetime.utcnow()
            )

            state["analysis_result"] = analysis_result
            state["processing_complete"] = True

            logger.info("Social sentiment analysis finalized")

        except Exception as e:
            logger.error(f"Error finalizing analysis: {e}")
            state["error_messages"].append(f"Finalization error: {str(e)}")

        return state

    async def analyze_social_sentiment(
        self,
        token_symbol: str,
        search_terms: Optional[List[str]] = None
    ) -> Optional[SocialSentimentResult]:
        """
        Main entry point for social sentiment analysis

        Args:
            token_symbol: Token symbol to analyze
            search_terms: Additional search terms

        Returns:
            Social sentiment analysis result or None if failed
        """

        if not search_terms:
            search_terms = [token_symbol, f"${token_symbol}"]

        # Initialize state
        initial_state = SocialSentimentState(
            token_symbol=token_symbol,
            search_terms=search_terms,
            reddit_posts=[],
            twitter_posts=[],
            telegram_posts=[],
            sentiment_analysis=None,
            influence_analysis=None,
            suspicious_activities=[],
            analysis_result=None,
            error_messages=[],
            processing_complete=False
        )

        try:
            final_state = await self.graph.ainvoke(initial_state)
            return final_state.get("analysis_result")

        except Exception as e:
            logger.error(f"Error in social sentiment analysis workflow: {e}")
            return None

    async def get_status(self) -> Dict[str, Any]:
        """Get current status of social sentiment agent"""
        return {
            "agent_type": "social_sentiment",
            "llm_available": self.llm is not None,
            "reddit_available": self.reddit_client_id is not None,
            "twitter_available": self.twitter_bearer_token is not None,
            "sentiment_keywords_loaded": len(self.sentiment_keywords),
            "suspicious_patterns_loaded": len(self.suspicious_patterns),
            "last_run": datetime.utcnow().isoformat()
        }
