[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "quantum-market-intelligence"
version = "1.0.0"
description = "Production-ready cryptocurrency rug detection and market intelligence system"
authors = [
    {name = "Quantum Market Intelligence Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Office/Business :: Financial",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    # Core Framework
    "fastapi==0.104.1",
    "uvicorn[standard]==0.24.0",
    "pydantic==2.5.0",
    "pydantic-settings==2.1.0",

    # Database & Cache
    "asyncpg==0.29.0",
    "redis[hiredis]==5.0.1",
    "sqlalchemy[asyncio]==2.0.23",

    # HTTP & API
    "requests==2.31.0",
    "aiohttp==3.9.1",
    "httpx==0.25.2",

    # Utilities
    "python-multipart==0.0.6",
    "python-dotenv==1.0.0",
    "structlog==23.2.0",
    "rich==13.7.0",
    "prometheus-client==0.19.0",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest==7.4.3",
    "pytest-asyncio==0.21.1",
    "pytest-cov==4.1.0",
    "pytest-mock==3.12.0",
    "pytest-xdist==3.5.0",
    "httpx==0.25.2",
    
    # Code Quality
    "black==23.11.0",
    "isort==5.12.0",
    "flake8==6.1.0",
    "mypy==1.7.1",
    "pre-commit==3.6.0",
    
    # Performance Testing
    "locust==2.17.0",
    
    # Security Testing
    "bandit==1.7.5",
    "safety==2.3.5",
]

production = [
    "gunicorn==21.2.0",
    "gevent==23.9.1",
]

[project.urls]
Homepage = "https://github.com/quantum-market-intelligence/quantum-market-intelligence"
Documentation = "https://docs.quantum-market.ai"
Repository = "https://github.com/quantum-market-intelligence/quantum-market-intelligence"
"Bug Tracker" = "https://github.com/quantum-market-intelligence/quantum-market-intelligence/issues"

[project.scripts]
qmi = "quantum_market_intelligence.cli:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["services*", "shared*"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["services", "shared"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "slither.*",
    "ccxt.*",
    "web3.*",
    "clickhouse_driver.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "e2e: marks tests as end-to-end tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["services", "shared"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "venv"]
skips = ["B101", "B601"]
