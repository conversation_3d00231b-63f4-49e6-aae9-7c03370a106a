Metadata-Version: 2.4
Name: quantum-market-intelligence
Version: 1.0.0
Summary: Production-ready cryptocurrency rug detection and market intelligence system
Author-email: Quantum Market Intelligence Team <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/quantum-market-intelligence/quantum-market-intelligence
Project-URL: Documentation, https://docs.quantum-market.ai
Project-URL: Repository, https://github.com/quantum-market-intelligence/quantum-market-intelligence
Project-URL: Bug Tracker, https://github.com/quantum-market-intelligence/quantum-market-intelligence/issues
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Financial and Insurance Industry
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Office/Business :: Financial
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.10
Description-Content-Type: text/markdown
Requires-Dist: fastapi==0.104.1
Requires-Dist: uvicorn[standard]==0.24.0
Requires-Dist: pydantic==2.5.0
Requires-Dist: pydantic-settings==2.1.0
Requires-Dist: asyncpg==0.29.0
Requires-Dist: redis[hiredis]==5.0.1
Requires-Dist: sqlalchemy[asyncio]==2.0.23
Requires-Dist: requests==2.31.0
Requires-Dist: aiohttp==3.9.1
Requires-Dist: httpx==0.25.2
Requires-Dist: python-multipart==0.0.6
Requires-Dist: python-dotenv==1.0.0
Requires-Dist: structlog==23.2.0
Requires-Dist: rich==13.7.0
Requires-Dist: prometheus-client==0.19.0
Provides-Extra: dev
Requires-Dist: pytest==7.4.3; extra == "dev"
Requires-Dist: pytest-asyncio==0.21.1; extra == "dev"
Requires-Dist: pytest-cov==4.1.0; extra == "dev"
Requires-Dist: pytest-mock==3.12.0; extra == "dev"
Requires-Dist: pytest-xdist==3.5.0; extra == "dev"
Requires-Dist: httpx==0.25.2; extra == "dev"
Requires-Dist: black==23.11.0; extra == "dev"
Requires-Dist: isort==5.12.0; extra == "dev"
Requires-Dist: flake8==6.1.0; extra == "dev"
Requires-Dist: mypy==1.7.1; extra == "dev"
Requires-Dist: pre-commit==3.6.0; extra == "dev"
Requires-Dist: locust==2.17.0; extra == "dev"
Requires-Dist: bandit==1.7.5; extra == "dev"
Requires-Dist: safety==2.3.5; extra == "dev"
Provides-Extra: production
Requires-Dist: gunicorn==21.2.0; extra == "production"
Requires-Dist: gevent==23.9.1; extra == "production"

# Quantum Market Intelligence System

A production-ready cryptocurrency rug detection and market intelligence system with 99.9th percentile quality standards.

## 🎯 Quality Standards & Success Criteria

- **Rug Detection Accuracy**: ≥98% with ≤3% false positive rate
- **System Uptime**: 99.9% availability
- **Response Time**: ≤100ms API response (P95)
- **Zero Hallucination Policy**: All APIs tested before implementation
- **Comprehensive Testing**: Unit, integration, and e2e test coverage

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    API Gateway (FastAPI)                    │
├─────────────────────────────────────────────────────────────┤
│  LangGraph Agentic Layer │  ML Detection Engine │ Cache    │
├─────────────────────────────────────────────────────────────┤
│     Data Sources Layer (Multi-API with Fallbacks)          │
├─────────────────────────────────────────────────────────────┤
│  Redis Cluster │ PostgreSQL │ ClickHouse (Time Series)     │
├─────────────────────────────────────────────────────────────┤
│        OpenTelemetry + Prometheus + Grafana                │
└─────────────────────────────────────────────────────────────┘
```

## 📁 Project Structure

```
quantum-market-intelligence/
├── services/
│   ├── api-gateway/          # FastAPI main entry point
│   ├── data-ingestion/       # Multi-source data collection
│   ├── rug-detection/        # ML-powered analysis engine
│   ├── market-intelligence/  # LangGraph agentic workflows
│   ├── notification/         # Alert distribution system
│   └── monitoring/          # Observability and health checks
├── shared/
│   ├── models/              # Shared data models
│   ├── utils/               # Common utilities
│   └── config/              # Configuration management
├── infrastructure/
│   ├── docker/              # Docker configurations
│   ├── k8s/                 # Kubernetes manifests
│   └── monitoring/          # Observability stack
├── tests/
│   ├── unit/                # Unit tests (90%+ coverage)
│   ├── integration/         # API integration tests
│   ├── e2e/                 # End-to-end testing
│   ├── performance/         # Load and stress testing
│   └── security/            # Security testing
├── docs/                    # Documentation
├── scripts/                 # Deployment and utility scripts
└── data/                    # Sample data and ML models
```

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7+

### Local Development Setup

1. **Clone and setup environment**:
```bash
cd quantum-market-intelligence
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Start infrastructure services**:
```bash
docker-compose up -d postgres redis clickhouse
```

3. **Run the application**:
```bash
python -m services.api_gateway.main
```

## 🔧 Technology Stack

### Core Technologies
- **Backend**: FastAPI, Python 3.11+
- **AI/ML**: LangGraph, LangChain, scikit-learn
- **Databases**: PostgreSQL, Redis, ClickHouse
- **Monitoring**: OpenTelemetry, Prometheus, Grafana, Jaeger
- **Containerization**: Docker, Kubernetes

### Data Sources
- **Primary**: DexScreener, CoinGecko, Etherscan
- **Fallback**: CoinAPI, Dune Analytics, Birdeye
- **Blockchain**: Infura (15+ chains), Alchemy

## 📊 Key Features

### 1. Multi-Source Data Ingestion
- Redundant API calls with automatic failover
- Rate limiting and circuit breaker patterns
- Real-time WebSocket feeds from multiple chains

### 2. Advanced Rug Detection
- Static contract analysis using Slither
- Dynamic behavior tracking with ML models
- Social sentiment analysis (Reddit, Twitter)
- Liquidity lock validation

### 3. LangGraph Agentic Workflows
- Intelligent token prioritization
- Multi-step analysis orchestration
- Confidence scoring (1-5 scale)
- Human-in-the-loop for borderline cases

### 4. Production-Grade Observability
- Distributed tracing with Jaeger
- Metrics collection with Prometheus
- Real-time dashboards with Grafana
- Automated alerting and incident response

## 🧪 Testing Strategy

- **Unit Tests**: 90%+ code coverage with pytest
- **Integration Tests**: API endpoint validation
- **E2E Tests**: Full workflow testing
- **Performance Tests**: Load testing with locust
- **Security Tests**: Vulnerability scanning
- **ML Validation**: Model accuracy and drift detection

## 📈 Monitoring & Metrics

### Key Performance Indicators
- Rug detection accuracy and false positive rate
- API response times and error rates
- System resource utilization
- Data freshness and processing latency
- User satisfaction and alert relevance

### Alerting Thresholds
- API response time > 100ms (P95)
- Error rate > 1%
- ML model accuracy < 95%
- System memory usage > 80%
- Queue depth > 1000 items

## 🔒 Security & Compliance

- API key rotation and secure storage
- Rate limiting and DDoS protection
- Input validation and sanitization
- Audit logging and compliance reporting
- Regular security assessments

## 📚 Documentation

- [API Documentation](docs/api.md)
- [Architecture Guide](docs/architecture.md)
- [Deployment Guide](docs/deployment.md)
- [Contributing Guidelines](docs/contributing.md)
- [Troubleshooting](docs/troubleshooting.md)

## 🤝 Contributing

Please read our [Contributing Guidelines](docs/contributing.md) before submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
