#!/usr/bin/env python3
"""
Test WebSocket streams functionality
"""

import asyncio
import json
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock


def test_websocket_config():
    """Test WebSocket configuration"""
    print("🧪 Testing WebSocket Configuration...")
    
    class StreamConfig:
        def __init__(self, url, subscription_message, reconnect_interval=5, max_reconnect_attempts=10, heartbeat_interval=30):
            self.url = url
            self.subscription_message = subscription_message
            self.reconnect_interval = reconnect_interval
            self.max_reconnect_attempts = max_reconnect_attempts
            self.heartbeat_interval = heartbeat_interval
    
    # Test Ethereum WebSocket config
    eth_config = StreamConfig(
        url="wss://mainnet.infura.io/ws/v3/test-key",
        subscription_message={
            "jsonrpc": "2.0",
            "id": 1,
            "method": "eth_subscribe",
            "params": [
                "logs",
                {
                    "topics": [
                        "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"  # Transfer event
                    ]
                }
            ]
        }
    )
    
    assert eth_config.url.startswith("wss://"), "WebSocket URL should use wss protocol"
    assert "eth_subscribe" in eth_config.subscription_message["method"], "Should subscribe to Ethereum events"
    assert eth_config.reconnect_interval == 5, "Default reconnect interval should be 5 seconds"
    print("✅ Ethereum WebSocket configuration valid")
    
    # Test Solana WebSocket config
    sol_config = StreamConfig(
        url="wss://api.mainnet-beta.solana.com",
        subscription_message={
            "jsonrpc": "2.0",
            "id": 1,
            "method": "programSubscribe",
            "params": [
                "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",  # PumpFun program
                {
                    "encoding": "jsonParsed",
                    "commitment": "confirmed"
                }
            ]
        }
    )
    
    assert "programSubscribe" in sol_config.subscription_message["method"], "Should subscribe to Solana program"
    assert "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P" in sol_config.subscription_message["params"], "Should monitor PumpFun program"
    print("✅ Solana WebSocket configuration valid")
    
    return True


def test_message_processing():
    """Test WebSocket message processing"""
    print("🧪 Testing Message Processing...")
    
    class MessageProcessor:
        def __init__(self):
            self.processed_messages = []
        
        def process_ethereum_log(self, data):
            """Process Ethereum log message"""
            if "params" in data and "result" in data["params"]:
                log_data = data["params"]["result"]
                
                contract_info = {
                    "contract_address": log_data.get("address"),
                    "transaction_hash": log_data.get("transactionHash"),
                    "block_number": log_data.get("blockNumber"),
                    "chain": "ethereum",
                    "timestamp": datetime.utcnow().isoformat(),
                    "source": "websocket"
                }
                
                self.processed_messages.append(contract_info)
                return contract_info
            return None
        
        def process_solana_program(self, data):
            """Process Solana program notification"""
            if "params" in data and "result" in data["params"]:
                result = data["params"]["result"]
                
                token_info = {
                    "contract_address": result.get("account"),
                    "program_id": result.get("owner"),
                    "chain": "solana",
                    "timestamp": datetime.utcnow().isoformat(),
                    "source": "websocket",
                    "raw_data": result
                }
                
                self.processed_messages.append(token_info)
                return token_info
            return None
    
    processor = MessageProcessor()
    
    # Test Ethereum log processing
    eth_message = {
        "jsonrpc": "2.0",
        "method": "eth_subscription",
        "params": {
            "subscription": "0x123",
            "result": {
                "address": "******************************************",
                "transactionHash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
                "blockNumber": "0x123456",
                "topics": ["0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"],
                "data": "0x0000000000000000000000000000000000000000000000000de0b6b3a7640000"
            }
        }
    }
    
    result = processor.process_ethereum_log(eth_message)
    assert result is not None, "Should process Ethereum log message"
    assert result["contract_address"] == "******************************************", "Should extract contract address"
    assert result["chain"] == "ethereum", "Should set correct chain"
    print("✅ Ethereum log processing working")
    
    # Test Solana program processing
    sol_message = {
        "jsonrpc": "2.0",
        "method": "programNotification",
        "params": {
            "subscription": 456,
            "result": {
                "account": "********************************",
                "owner": "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",
                "data": "base64encodeddata"
            }
        }
    }
    
    result = processor.process_solana_program(sol_message)
    assert result is not None, "Should process Solana program message"
    assert result["contract_address"] == "********************************", "Should extract account address"
    assert result["chain"] == "solana", "Should set correct chain"
    print("✅ Solana program processing working")
    
    assert len(processor.processed_messages) == 2, "Should have processed 2 messages"
    print("✅ Message processing working")
    
    return True


async def test_connection_management():
    """Test WebSocket connection management"""
    print("🧪 Testing Connection Management...")
    
    class MockWebSocket:
        def __init__(self, should_fail=False, messages=None):
            self.should_fail = should_fail
            self.messages = messages or []
            self.closed = False
            self.sent_messages = []
        
        async def send(self, message):
            if self.should_fail:
                raise Exception("Connection failed")
            self.sent_messages.append(message)
        
        async def __aiter__(self):
            for message in self.messages:
                if self.closed:
                    break
                yield message
        
        async def close(self):
            self.closed = True
    
    class ConnectionManager:
        def __init__(self):
            self.reconnect_attempts = 0
            self.max_reconnect_attempts = 3
            self.connected = False
        
        async def connect_with_retry(self, websocket_factory):
            """Connect with retry logic"""
            while self.reconnect_attempts < self.max_reconnect_attempts:
                try:
                    websocket = websocket_factory()
                    
                    # Test connection by sending a message
                    await websocket.send('{"test": "connection"}')
                    
                    self.connected = True
                    self.reconnect_attempts = 0  # Reset on success
                    return websocket
                    
                except Exception as e:
                    self.reconnect_attempts += 1
                    self.connected = False
                    
                    if self.reconnect_attempts >= self.max_reconnect_attempts:
                        raise Exception(f"Max reconnection attempts reached: {e}")
                    
                    # Exponential backoff
                    backoff_time = 0.1 * (2 ** self.reconnect_attempts)
                    await asyncio.sleep(backoff_time)
            
            raise Exception("Failed to connect")
    
    manager = ConnectionManager()
    
    # Test successful connection
    def successful_websocket():
        return MockWebSocket(should_fail=False)
    
    websocket = await manager.connect_with_retry(successful_websocket)
    assert manager.connected, "Should be connected after successful connection"
    assert manager.reconnect_attempts == 0, "Reconnect attempts should be reset"
    assert len(websocket.sent_messages) == 1, "Should have sent connection test message"
    print("✅ Successful connection working")
    
    # Test connection with retries
    manager = ConnectionManager()
    call_count = 0
    
    def failing_then_success_websocket():
        nonlocal call_count
        call_count += 1
        if call_count <= 2:  # Fail first 2 attempts
            return MockWebSocket(should_fail=True)
        else:
            return MockWebSocket(should_fail=False)
    
    websocket = await manager.connect_with_retry(failing_then_success_websocket)
    assert manager.connected, "Should eventually connect"
    assert call_count == 3, "Should have tried 3 times"
    print("✅ Connection retry logic working")
    
    # Test max retry limit
    manager = ConnectionManager()
    
    def always_failing_websocket():
        return MockWebSocket(should_fail=True)
    
    try:
        await manager.connect_with_retry(always_failing_websocket)
        assert False, "Should have raised exception after max retries"
    except Exception as e:
        assert "Max reconnection attempts reached" in str(e), "Should fail after max retries"
        assert not manager.connected, "Should not be connected"
        print("✅ Max retry limit working")
    
    return True


async def test_message_routing():
    """Test message routing to handlers"""
    print("🧪 Testing Message Routing...")
    
    class MessageRouter:
        def __init__(self):
            self.handlers = {
                "new_contracts": [],
                "token_transfers": [],
                "price_updates": []
            }
        
        def register_handler(self, message_type, handler):
            """Register a handler for a message type"""
            if message_type in self.handlers:
                self.handlers[message_type].append(handler)
        
        async def route_message(self, message_type, data):
            """Route message to all registered handlers"""
            results = []
            for handler in self.handlers.get(message_type, []):
                try:
                    result = await handler(data)
                    results.append(result)
                except Exception as e:
                    results.append({"error": str(e)})
            return results
    
    router = MessageRouter()
    
    # Create test handlers
    async def contract_handler(data):
        return {"handler": "contract", "processed": data.get("contract_address")}
    
    async def transfer_handler(data):
        return {"handler": "transfer", "processed": data.get("amount")}
    
    async def failing_handler(data):
        raise Exception("Handler failed")
    
    # Register handlers
    router.register_handler("new_contracts", contract_handler)
    router.register_handler("token_transfers", transfer_handler)
    router.register_handler("token_transfers", failing_handler)  # Test error handling
    
    # Test contract message routing
    contract_data = {"contract_address": "0x1234", "chain": "ethereum"}
    results = await router.route_message("new_contracts", contract_data)
    
    assert len(results) == 1, "Should have 1 result for contract message"
    assert results[0]["handler"] == "contract", "Should route to contract handler"
    assert results[0]["processed"] == "0x1234", "Should process contract address"
    print("✅ Contract message routing working")
    
    # Test transfer message routing with error handling
    transfer_data = {"amount": "1000", "token": "0x5678"}
    results = await router.route_message("token_transfers", transfer_data)
    
    assert len(results) == 2, "Should have 2 results for transfer message"
    assert results[0]["handler"] == "transfer", "First handler should succeed"
    assert "error" in results[1], "Second handler should fail gracefully"
    print("✅ Transfer message routing with error handling working")
    
    # Test unknown message type
    results = await router.route_message("unknown_type", {})
    assert len(results) == 0, "Should return empty results for unknown message type"
    print("✅ Unknown message type handling working")
    
    return True


def test_stream_health_monitoring():
    """Test stream health monitoring"""
    print("🧪 Testing Stream Health Monitoring...")
    
    class StreamHealthMonitor:
        def __init__(self):
            self.streams = {}
        
        def register_stream(self, stream_name, config):
            """Register a stream for monitoring"""
            self.streams[stream_name] = {
                "config": config,
                "status": "disconnected",
                "last_message_time": None,
                "message_count": 0,
                "error_count": 0,
                "reconnect_count": 0
            }
        
        def update_stream_status(self, stream_name, status, error=None):
            """Update stream status"""
            if stream_name in self.streams:
                self.streams[stream_name]["status"] = status
                if error:
                    self.streams[stream_name]["error_count"] += 1
                if status == "reconnecting":
                    self.streams[stream_name]["reconnect_count"] += 1
        
        def record_message(self, stream_name):
            """Record a message received from stream"""
            if stream_name in self.streams:
                self.streams[stream_name]["message_count"] += 1
                self.streams[stream_name]["last_message_time"] = datetime.utcnow()
        
        def get_health_status(self):
            """Get overall health status"""
            if not self.streams:
                return {"healthy": False, "reason": "No streams registered"}
            
            connected_streams = sum(1 for s in self.streams.values() if s["status"] == "connected")
            total_streams = len(self.streams)
            
            health_percentage = (connected_streams / total_streams) * 100
            
            return {
                "healthy": health_percentage >= 50,  # At least 50% streams connected
                "connected_streams": connected_streams,
                "total_streams": total_streams,
                "health_percentage": health_percentage,
                "streams": self.streams
            }
    
    monitor = StreamHealthMonitor()
    
    # Register test streams
    monitor.register_stream("ethereum_mainnet", {"url": "wss://eth.example.com"})
    monitor.register_stream("solana_mainnet", {"url": "wss://sol.example.com"})
    
    # Initially should be unhealthy (no connections)
    health = monitor.get_health_status()
    assert not health["healthy"], "Should be unhealthy with no connections"
    assert health["connected_streams"] == 0, "Should have 0 connected streams"
    print("✅ Initial unhealthy state working")
    
    # Connect one stream
    monitor.update_stream_status("ethereum_mainnet", "connected")
    monitor.record_message("ethereum_mainnet")
    
    health = monitor.get_health_status()
    assert health["healthy"], "Should be healthy with 50% streams connected"
    assert health["connected_streams"] == 1, "Should have 1 connected stream"
    assert health["health_percentage"] == 50.0, "Should be 50% healthy"
    print("✅ Partial health status working")
    
    # Connect second stream
    monitor.update_stream_status("solana_mainnet", "connected")
    monitor.record_message("solana_mainnet")
    
    health = monitor.get_health_status()
    assert health["healthy"], "Should be healthy with all streams connected"
    assert health["connected_streams"] == 2, "Should have 2 connected streams"
    assert health["health_percentage"] == 100.0, "Should be 100% healthy"
    print("✅ Full health status working")
    
    # Test error tracking
    monitor.update_stream_status("ethereum_mainnet", "error", error="Connection lost")
    
    health = monitor.get_health_status()
    assert health["streams"]["ethereum_mainnet"]["error_count"] == 1, "Should track errors"
    assert health["connected_streams"] == 1, "Should have 1 connected stream after error"
    print("✅ Error tracking working")
    
    return True


async def main():
    """Run all WebSocket tests"""
    print("🚀 Starting WebSocket Streams Tests\n")
    
    tests = [
        ("WebSocket Configuration", test_websocket_config),
        ("Message Processing", test_message_processing),
        ("Connection Management", test_connection_management),
        ("Message Routing", test_message_routing),
        ("Stream Health Monitoring", test_stream_health_monitoring),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running {test_name} Tests")
        print('='*50)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                print(f"✅ {test_name} tests PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} tests FAILED")
        except Exception as e:
            print(f"❌ {test_name} tests FAILED with exception: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*50}")
    print(f"TEST SUMMARY: {passed}/{total} test suites passed")
    print('='*50)
    
    if passed == total:
        print("🎉 All WebSocket streams tests passed!")
        return True
    else:
        print("⚠️  Some tests failed. Please review the output above.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
