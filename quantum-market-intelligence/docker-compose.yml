version: '3.8'

services:
  # Database Services
  postgres:
    image: postgres:15-alpine
    container_name: qmi-postgres
    environment:
      POSTGRES_DB: quantum_market_intelligence
      POSTGRES_USER: qmi_user
      POSTGRES_PASSWORD: qmi_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U qmi_user -d quantum_market_intelligence"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - qmi-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: qmi-redis
    ports:
      - "6382:6379"
    volumes:
      - redis_data:/data
      - ./infrastructure/docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - qmi-network

  # ClickHouse for Time Series Data
  clickhouse:
    image: clickhouse/clickhouse-server:23.8-alpine
    container_name: qmi-clickhouse
    ports:
      - "8123:8123"
      - "9000:9000"
    volumes:
      - clickhouse_data:/var/lib/clickhouse
    environment:
      CLICKHOUSE_DB: market_data
      CLICKHOUSE_USER: qmi_user
      CLICKHOUSE_PASSWORD: qmi_password
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - qmi-network

  # Monitoring Stack
  jaeger:
    image: jaegertracing/all-in-one:1.50
    container_name: qmi-jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      COLLECTOR_OTLP_ENABLED: true
    networks:
      - qmi-network

  prometheus:
    image: prom/prometheus:v2.47.2
    container_name: qmi-prometheus
    ports:
      - "9091:9090"
    volumes:
      - ./infrastructure/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - qmi-network

  grafana:
    image: grafana/grafana:10.2.0
    container_name: qmi-grafana
    ports:
      - "3002:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: false
    networks:
      - qmi-network

  # Message Queue
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: qmi-rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: qmi_user
      RABBITMQ_DEFAULT_PASS: qmi_password
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - qmi-network

  # Application Services (will be added in later phases)
  api-gateway:
    build:
      context: .
      dockerfile: services/api-gateway/Dockerfile
    container_name: qmi-api-gateway
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=************************************************/quantum_market_intelligence
      - REDIS_URL=redis://redis:6379/0
      - CLICKHOUSE_URL=http://clickhouse:8123
      - JAEGER_ENDPOINT=http://jaeger:14268/api/traces
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      clickhouse:
        condition: service_healthy
    networks:
      - qmi-network
    profiles:
      - app

volumes:
  postgres_data:
  redis_data:
  clickhouse_data:
  prometheus_data:
  grafana_data:
  rabbitmq_data:

networks:
  qmi-network:
    driver: bridge
