#!/usr/bin/env python3
"""
Simple test for core functionality
"""

import asyncio
import time
from datetime import datetime


def test_data_validator():
    """Test data validator functionality"""
    print("🧪 Testing Data Validator...")
    
    class DataValidator:
        def _validate_ethereum_address(self, address: str) -> bool:
            if not address.startswith("0x"):
                return False
            if len(address) != 42:
                return False
            try:
                int(address[2:], 16)
                return True
            except ValueError:
                return False
        
        def _validate_solana_address(self, address: str) -> bool:
            if not address or len(address) < 32 or len(address) > 44:
                return False
            base58_chars = "**********************************************************"
            return all(c in base58_chars for c in address)
        
        def validate_token_data(self, token_data: dict) -> bool:
            required_fields = ["contract_address", "chain"]
            for field in required_fields:
                if not token_data.get(field):
                    return False
            
            contract_address = token_data["contract_address"]
            chain = token_data["chain"]
            
            if chain in ["ethereum", "bsc", "polygon"]:
                return self._validate_ethereum_address(contract_address)
            elif chain == "solana":
                return self._validate_solana_address(contract_address)
            
            return True
    
    validator = DataValidator()
    
    # Test Ethereum address validation
    valid_eth = "******************************************"
    invalid_eth = "invalid_address"
    
    assert validator._validate_ethereum_address(valid_eth), "Valid Ethereum address should pass"
    assert not validator._validate_ethereum_address(invalid_eth), "Invalid Ethereum address should fail"
    print("✅ Ethereum address validation working")
    
    # Test Solana address validation
    valid_sol = "********************************"
    invalid_sol = "0x1234"
    
    assert validator._validate_solana_address(valid_sol), "Valid Solana address should pass"
    assert not validator._validate_solana_address(invalid_sol), "Invalid Solana address should fail"
    print("✅ Solana address validation working")
    
    # Test token data validation
    valid_token = {
        "contract_address": "******************************************",
        "chain": "ethereum",
        "symbol": "TEST"
    }
    
    invalid_token = {
        "symbol": "TEST"
        # Missing required fields
    }
    
    assert validator.validate_token_data(valid_token), "Valid token should pass"
    assert not validator.validate_token_data(invalid_token), "Invalid token should fail"
    print("✅ Token data validation working")
    
    return True


def test_rate_limiter():
    """Test rate limiter functionality"""
    print("🧪 Testing Rate Limiter...")
    
    class TokenBucket:
        def __init__(self, capacity: int, refill_rate: float):
            self.capacity = capacity
            self.tokens = capacity
            self.refill_rate = refill_rate
            self.last_refill = time.time()
        
        def acquire(self, tokens: int = 1) -> bool:
            current_time = time.time()
            time_elapsed = current_time - self.last_refill
            tokens_to_add = time_elapsed * self.refill_rate
            
            self.tokens = min(self.capacity, self.tokens + tokens_to_add)
            self.last_refill = current_time
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False
    
    # Test token bucket
    bucket = TokenBucket(capacity=10, refill_rate=1.0)  # 1 token per second
    
    # Should be able to acquire tokens initially
    assert bucket.acquire(5), "Should acquire tokens from full bucket"
    assert bucket.tokens == 5, "Tokens should be decremented"
    print("✅ Token bucket acquisition working")
    
    # Should fail when exhausted
    assert not bucket.acquire(10), "Should fail when not enough tokens"
    print("✅ Token bucket exhaustion working")
    
    # Test refill (simulate time passing)
    time.sleep(1.1)  # Wait for refill
    assert bucket.acquire(1), "Should acquire after refill"
    print("✅ Token bucket refill working")
    
    return True


def test_circuit_breaker():
    """Test circuit breaker functionality"""
    print("🧪 Testing Circuit Breaker...")
    
    class CircuitBreaker:
        def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
            self.failure_threshold = failure_threshold
            self.recovery_timeout = recovery_timeout
            self.failures = 0
            self.last_failure_time = None
            self.is_open = False
        
        def record_failure(self):
            self.failures += 1
            self.last_failure_time = time.time()
            
            if self.failures >= self.failure_threshold:
                self.is_open = True
        
        def record_success(self):
            self.failures = 0
            self.is_open = False
            self.last_failure_time = None
        
        def can_execute(self) -> bool:
            if not self.is_open:
                return True
            
            # Check if recovery timeout has passed
            if self.last_failure_time:
                time_since_failure = time.time() - self.last_failure_time
                if time_since_failure > self.recovery_timeout:
                    self.is_open = False
                    self.failures = 0
                    return True
            
            return False
    
    breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=1)
    
    # Initially should allow execution
    assert breaker.can_execute(), "Circuit breaker should initially be closed"
    print("✅ Circuit breaker initially closed")
    
    # Record failures to open it
    for i in range(3):
        breaker.record_failure()
    
    assert not breaker.can_execute(), "Circuit breaker should be open after failures"
    print("✅ Circuit breaker opens after failures")
    
    # Test recovery
    time.sleep(1.1)  # Wait for recovery timeout
    assert breaker.can_execute(), "Circuit breaker should recover after timeout"
    print("✅ Circuit breaker recovery working")
    
    # Test success reset
    breaker.record_failure()
    breaker.record_success()
    assert breaker.can_execute(), "Success should reset circuit breaker"
    assert breaker.failures == 0, "Failures should be reset"
    print("✅ Circuit breaker success reset working")
    
    return True


async def test_async_functionality():
    """Test async functionality"""
    print("🧪 Testing Async Functionality...")
    
    async def mock_api_call(delay: float = 0.1, should_fail: bool = False):
        await asyncio.sleep(delay)
        if should_fail:
            raise Exception("Mock API failure")
        return {"status": "success", "data": []}
    
    # Test successful async call
    result = await mock_api_call()
    assert result["status"] == "success", "Async call should succeed"
    print("✅ Async API call working")
    
    # Test async error handling
    try:
        await mock_api_call(should_fail=True)
        assert False, "Should have raised exception"
    except Exception as e:
        assert "Mock API failure" in str(e), "Should catch expected exception"
        print("✅ Async error handling working")
    
    # Test concurrent calls
    tasks = [mock_api_call(0.1) for _ in range(3)]
    results = await asyncio.gather(*tasks)
    assert len(results) == 3, "Should handle concurrent calls"
    assert all(r["status"] == "success" for r in results), "All calls should succeed"
    print("✅ Concurrent async calls working")
    
    return True


def test_token_deduplication():
    """Test token deduplication logic"""
    print("🧪 Testing Token Deduplication...")
    
    def deduplicate_tokens(tokens):
        seen_addresses = set()
        unique_tokens = []
        
        for token in tokens:
            key = (token.get("contract_address", "").lower(), token.get("chain", "").lower())
            if key not in seen_addresses and key[0] and key[1]:
                seen_addresses.add(key)
                unique_tokens.append(token)
        
        return unique_tokens
    
    tokens = [
        {"contract_address": "0x1234", "chain": "ethereum", "symbol": "TEST1"},
        {"contract_address": "0x1234", "chain": "ethereum", "symbol": "TEST2"},  # Duplicate
        {"contract_address": "0x5678", "chain": "ethereum", "symbol": "TEST3"},
        {"contract_address": "0x1234", "chain": "bsc", "symbol": "TEST4"},  # Different chain
    ]
    
    unique_tokens = deduplicate_tokens(tokens)
    
    assert len(unique_tokens) == 3, f"Expected 3 unique tokens, got {len(unique_tokens)}"
    
    # Check that we have the right tokens
    addresses = [(t["contract_address"], t["chain"]) for t in unique_tokens]
    expected = [("0x1234", "ethereum"), ("0x5678", "ethereum"), ("0x1234", "bsc")]
    
    for expected_token in expected:
        assert expected_token in addresses, f"Missing expected token: {expected_token}"
    
    print("✅ Token deduplication working")
    return True


async def main():
    """Run all tests"""
    print("🚀 Starting Core Functionality Tests\n")
    
    tests = [
        ("Data Validator", test_data_validator),
        ("Rate Limiter", test_rate_limiter),
        ("Circuit Breaker", test_circuit_breaker),
        ("Token Deduplication", test_token_deduplication),
        ("Async Functionality", test_async_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running {test_name} Tests")
        print('='*50)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                print(f"✅ {test_name} tests PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} tests FAILED")
        except Exception as e:
            print(f"❌ {test_name} tests FAILED with exception: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*50}")
    print(f"TEST SUMMARY: {passed}/{total} test suites passed")
    print('='*50)
    
    if passed == total:
        print("🎉 All core functionality tests passed!")
        return True
    else:
        print("⚠️  Some tests failed. Please review the output above.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
