# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# AI/ML Stack - Updated for 2025
langgraph==0.2.16
langchain==0.3.0
langchain-openai==0.2.0
langchain-community==0.3.0
openai==1.51.0
scikit-learn==1.4.0
numpy==1.26.4
pandas==2.2.0
textblob==0.18.0
vaderSentiment==3.3.2

# Database & Cache
asyncpg==0.29.0
redis[hiredis]==5.0.1
clickhouse-driver==0.2.6
sqlalchemy[asyncio]==2.0.23
alembic==1.13.1

# Blockchain & Crypto APIs
web3==6.11.3
ccxt==4.1.77
requests==2.31.0
aiohttp==3.9.1
websockets==12.0

# Security & Analysis
slither-analyzer==0.10.0
cryptography==41.0.8
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Monitoring & Observability
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
opentelemetry-instrumentation-requests==0.42b0
opentelemetry-instrumentation-redis==0.42b0
opentelemetry-instrumentation-sqlalchemy==0.42b0
opentelemetry-exporter-jaeger==1.21.0
opentelemetry-exporter-prometheus==1.12.0rc1
prometheus-client==0.19.0

# Utilities
python-multipart==0.0.6
python-dotenv==1.0.0
structlog==23.2.0
rich==13.7.0
typer==0.9.0
schedule==1.2.0
tenacity==8.2.3
httpx==0.25.2
