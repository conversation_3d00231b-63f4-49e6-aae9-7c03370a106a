"""
User management models
"""

from typing import Optional, Dict, Any, List

from pydantic import Field, EmailStr

from .base import DatabaseModel


class User(DatabaseModel):
    """User model"""
    
    email: EmailStr = Field(..., description="User email address")
    username: str = Field(..., description="Username")
    password_hash: str = Field(..., description="Hashed password")
    is_active: bool = Field(default=True, description="Whether user is active")
    is_verified: bool = Field(default=False, description="Whether email is verified")
    subscription_tier: str = Field(default="free", description="Subscription tier")


class UserPreferences(DatabaseModel):
    """User preferences and settings"""
    
    user_id: str = Field(..., description="Reference to user")
    notification_channels: Dict[str, bool] = Field(
        default_factory=lambda: {"email": True, "telegram": False},
        description="Notification channel preferences"
    )
    alert_thresholds: Dict[str, Any] = Field(
        default_factory=lambda: {"min_confidence": 3.0, "severity_levels": ["medium", "high", "critical"]},
        description="Alert threshold settings"
    )
    watched_tokens: List[str] = Field(default_factory=list, description="List of watched token addresses")
