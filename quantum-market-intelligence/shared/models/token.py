"""
Token-related data models
"""

from decimal import Decimal
from enum import Enum
from typing import Optional
from uuid import UUID

from pydantic import Field, field_validator

from .base import DatabaseModel


class ChainType(str, Enum):
    """Supported blockchain networks"""
    
    ETHEREUM = "ethereum"
    BSC = "bsc"
    POLYGON = "polygon"
    ARBITRUM = "arbitrum"
    OPTIMISM = "optimism"
    BASE = "base"
    AVALANCHE = "avalanche"


class TokenStatus(str, Enum):
    """Token status enumeration"""
    
    ACTIVE = "active"
    FLAGGED = "flagged"
    VERIFIED = "verified"
    RUGGED = "rugged"


class Token(DatabaseModel):
    """Token model representing a cryptocurrency token"""
    
    contract_address: str = Field(..., min_length=42, max_length=42, description="Token contract address")
    chain: ChainType = Field(..., description="Blockchain network")
    symbol: str = Field(..., min_length=1, max_length=20, description="Token symbol")
    name: str = Field(..., min_length=1, max_length=100, description="Token name")
    decimals: int = Field(default=18, ge=0, le=255, description="Token decimals")
    total_supply: Optional[Decimal] = Field(default=None, description="Total token supply")
    status: TokenStatus = Field(default=TokenStatus.ACTIVE, description="Token status")
    
    @field_validator("contract_address")
    @classmethod
    def validate_contract_address(cls, v: str) -> str:
        """Validate Ethereum-style contract address"""
        if not v.startswith("0x"):
            raise ValueError("Contract address must start with 0x")
        if len(v) != 42:
            raise ValueError("Contract address must be 42 characters long")
        try:
            int(v, 16)  # Validate hex format
        except ValueError:
            raise ValueError("Contract address must be valid hexadecimal")
        return v.lower()
    
    @field_validator("symbol")
    @classmethod
    def validate_symbol(cls, v: str) -> str:
        """Validate token symbol"""
        return v.upper().strip()
    
    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate token name"""
        return v.strip()
    
    class Config:
        json_schema_extra = {
            "example": {
                "contract_address": "******************************************",
                "chain": "ethereum",
                "symbol": "QMI",
                "name": "Quantum Market Intelligence",
                "decimals": 18,
                "total_supply": "1000000000000000000000000",
                "status": "active"
            }
        }


class TokenCreate(DatabaseModel):
    """Model for creating new tokens"""
    
    contract_address: str = Field(..., min_length=42, max_length=42)
    chain: ChainType
    symbol: str = Field(..., min_length=1, max_length=20)
    name: str = Field(..., min_length=1, max_length=100)
    decimals: int = Field(default=18, ge=0, le=255)
    total_supply: Optional[Decimal] = None


class TokenUpdate(DatabaseModel):
    """Model for updating tokens"""
    
    symbol: Optional[str] = Field(None, min_length=1, max_length=20)
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    decimals: Optional[int] = Field(None, ge=0, le=255)
    total_supply: Optional[Decimal] = None
    status: Optional[TokenStatus] = None


class TokenFilter(DatabaseModel):
    """Filter parameters for token queries"""
    
    chain: Optional[ChainType] = None
    status: Optional[TokenStatus] = None
    symbol: Optional[str] = None
    name_contains: Optional[str] = None
    contract_address: Optional[str] = None


class TokenResponse(Token):
    """Token response model with additional computed fields"""
    
    price_usd: Optional[Decimal] = Field(None, description="Current price in USD")
    market_cap: Optional[Decimal] = Field(None, description="Market capitalization")
    volume_24h: Optional[Decimal] = Field(None, description="24-hour trading volume")
    liquidity_usd: Optional[Decimal] = Field(None, description="Total liquidity in USD")
    risk_score: Optional[float] = Field(None, ge=0, le=5, description="Risk score (1-5)")
    confidence_score: Optional[float] = Field(None, ge=0, le=5, description="Analysis confidence (1-5)")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "contract_address": "******************************************",
                "chain": "ethereum",
                "symbol": "QMI",
                "name": "Quantum Market Intelligence",
                "decimals": 18,
                "total_supply": "1000000000000000000000000",
                "status": "active",
                "price_usd": "1.25",
                "market_cap": "1250000000",
                "volume_24h": "5000000",
                "liquidity_usd": "2500000",
                "risk_score": 2.1,
                "confidence_score": 4.5,
                "created_at": "2025-01-01T00:00:00Z",
                "updated_at": "2025-01-01T12:00:00Z"
            }
        }
