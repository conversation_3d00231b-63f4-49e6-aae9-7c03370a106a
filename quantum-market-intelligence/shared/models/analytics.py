"""
Analytics and metrics models
"""

from typing import Optional, Dict, Any

from pydantic import Field

from .base import DatabaseModel


class SystemMetric(DatabaseModel):
    """System metrics model"""
    
    metric_name: str = Field(..., description="Metric name")
    metric_value: float = Field(..., description="Metric value")
    tags: Dict[str, Any] = Field(default_factory=dict, description="Metric tags")


class ModelPerformance(DatabaseModel):
    """ML model performance metrics"""
    
    model_name: str = Field(..., description="Model name")
    model_version: str = Field(..., description="Model version")
    accuracy: Optional[float] = Field(None, description="Model accuracy")
    precision_score: Optional[float] = Field(None, description="Precision score")
    recall: Optional[float] = Field(None, description="Recall score")
    f1_score: Optional[float] = Field(None, description="F1 score")
    false_positive_rate: Optional[float] = Field(None, description="False positive rate")
    evaluation_timestamp: str = Field(..., description="When evaluation was performed")
