"""
Rug detection models
"""

from enum import Enum
from typing import Optional, Dict, Any
from uuid import UUID

from pydantic import Field

from .base import DatabaseModel


class AlertSeverity(str, Enum):
    """Alert severity levels"""
    
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ContractAnalysis(DatabaseModel):
    """Contract analysis results"""
    
    token_id: UUID = Field(..., description="Reference to token")
    bytecode_hash: str = Field(..., description="Contract bytecode hash")
    source_code: Optional[str] = Field(None, description="Contract source code")
    compiler_version: Optional[str] = Field(None, description="Solidity compiler version")
    optimization_enabled: Optional[bool] = Field(None, description="Whether optimization was enabled")
    vulnerability_score: int = Field(default=0, description="Vulnerability score (0-100)")
    risk_factors: Dict[str, Any] = Field(default_factory=dict, description="Identified risk factors")


class BehavioralAnalysis(DatabaseModel):
    """Behavioral analysis results"""
    
    token_id: UUID = Field(..., description="Reference to token")
    deployer_address: str = Field(..., description="Token deployer address")
    deployer_token_percentage: Optional[float] = Field(None, description="Deployer token holdings percentage")
    large_holder_concentration: Optional[float] = Field(None, description="Large holder concentration")
    transaction_pattern_score: Optional[int] = Field(None, description="Transaction pattern analysis score")
    social_activity_score: Optional[int] = Field(None, description="Social activity score")
    confidence_score: Optional[float] = Field(None, description="Analysis confidence score")
    risk_level: Optional[AlertSeverity] = Field(None, description="Overall risk level")


class Alert(DatabaseModel):
    """Alert model for notifications"""
    
    token_id: UUID = Field(..., description="Reference to token")
    alert_type: str = Field(..., description="Type of alert")
    severity: AlertSeverity = Field(..., description="Alert severity")
    title: str = Field(..., description="Alert title")
    description: Optional[str] = Field(None, description="Alert description")
    confidence_score: float = Field(..., description="Confidence score")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional alert metadata")
    is_sent: bool = Field(default=False, description="Whether alert has been sent")
    sent_at: Optional[str] = Field(None, description="When alert was sent")
