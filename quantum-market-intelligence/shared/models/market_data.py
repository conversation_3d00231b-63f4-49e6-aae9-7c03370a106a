"""
Market data models
"""

from decimal import Decimal
from typing import Optional
from uuid import UUID

from pydantic import Field

from .base import DatabaseModel
from .token import ChainType


class PriceData(DatabaseModel):
    """Price data model for token pricing information"""
    
    token_id: UUID = Field(..., description="Reference to token")
    price_usd: Decimal = Field(..., description="Price in USD")
    volume_24h: Optional[Decimal] = Field(None, description="24-hour trading volume")
    market_cap: Optional[Decimal] = Field(None, description="Market capitalization")
    liquidity_usd: Optional[Decimal] = Field(None, description="Total liquidity in USD")


class LiquidityPool(DatabaseModel):
    """Liquidity pool information"""
    
    token_id: UUID = Field(..., description="Reference to token")
    pool_address: str = Field(..., description="Pool contract address")
    dex_name: str = Field(..., description="DEX name (e.g., Uniswap, PancakeSwap)")
    pair_token: str = Field(..., description="Paired token address")
    liquidity_usd: Optional[Decimal] = Field(None, description="Pool liquidity in USD")
    volume_24h: Optional[Decimal] = Field(None, description="24-hour volume")
    is_locked: bool = Field(default=False, description="Whether liquidity is locked")
    lock_until: Optional[str] = Field(None, description="Lock expiration timestamp")
