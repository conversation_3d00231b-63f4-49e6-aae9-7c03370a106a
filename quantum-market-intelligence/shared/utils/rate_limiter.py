"""
Rate limiting utilities
"""

import time
from typing import Optional

from shared.utils.cache import cache_manager, CacheKeys
from shared.utils.logging import get_logger

logger = get_logger(__name__)


class RateLimiter:
    """Rate limiter using Redis for distributed rate limiting"""
    
    def __init__(self, requests_per_minute: int = 60, window_size: int = 60):
        self.requests_per_minute = requests_per_minute
        self.window_size = window_size
    
    async def is_allowed(self, identifier: str) -> tuple[bool, int]:
        """
        Check if request is allowed under rate limit
        
        Args:
            identifier: Unique identifier (e.g., IP address, user ID)
            
        Returns:
            Tuple of (is_allowed, remaining_requests)
        """
        try:
            current_window = int(time.time()) // self.window_size
            key = CacheKeys.rate_limit(identifier, str(current_window))
            
            # Get current count and increment
            current_count = await cache_manager.increment(key, amount=1, ttl=self.window_size)
            
            # Check if limit exceeded
            is_allowed = current_count <= self.requests_per_minute
            remaining = max(0, self.requests_per_minute - current_count)
            
            if not is_allowed:
                logger.warning(
                    "Rate limit exceeded",
                    identifier=identifier,
                    current_count=current_count,
                    limit=self.requests_per_minute
                )
            
            return is_allowed, remaining
            
        except Exception as e:
            logger.error("Rate limiter error", identifier=identifier, error=str(e))
            # Fail open - allow request if rate limiter fails
            return True, self.requests_per_minute
    
    async def reset(self, identifier: str) -> bool:
        """
        Reset rate limit for identifier
        
        Args:
            identifier: Unique identifier to reset
            
        Returns:
            True if reset successful
        """
        try:
            current_window = int(time.time()) // self.window_size
            key = CacheKeys.rate_limit(identifier, str(current_window))
            
            return await cache_manager.delete(key)
            
        except Exception as e:
            logger.error("Rate limit reset failed", identifier=identifier, error=str(e))
            return False
