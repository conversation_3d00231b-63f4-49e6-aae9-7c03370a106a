"""
Redis cache management utilities
"""

import json
import pickle
from typing import Any, Optional, Union, Dict, List
from datetime import datetime, timedelta

import redis.asyncio as redis
from redis.asyncio import Redis

from shared.config.settings import settings
from shared.utils.logging import get_logger

logger = get_logger(__name__)


class CacheManager:
    """
    Redis cache manager with async support and advanced features
    """
    
    def __init__(self, redis_client: Optional[Redis] = None):
        """
        Initialize cache manager
        
        Args:
            redis_client: Optional Redis client instance
        """
        self.redis = redis_client or self._create_redis_client()
        self.default_ttl = settings.app.cache_ttl_seconds
    
    def _create_redis_client(self) -> Redis:
        """Create Redis client from settings"""
        return redis.from_url(
            settings.database.redis_url,
            encoding="utf-8",
            decode_responses=False,  # We handle encoding ourselves
            socket_connect_timeout=5,
            socket_timeout=5,
            retry_on_timeout=True,
            health_check_interval=30,
        )
    
    async def get(self, key: str, default: Any = None) -> Any:
        """
        Get value from cache
        
        Args:
            key: Cache key
            default: Default value if key not found
            
        Returns:
            Cached value or default
        """
        try:
            value = await self.redis.get(key)
            if value is None:
                return default
            
            # Try to deserialize as JSON first, then pickle
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return pickle.loads(value)
                
        except Exception as e:
            logger.warning("Cache get failed", key=key, error=str(e))
            return default
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        serialize_json: bool = True
    ) -> bool:
        """
        Set value in cache
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (uses default if None)
            serialize_json: Whether to serialize as JSON (faster) or pickle
            
        Returns:
            True if successful, False otherwise
        """
        try:
            ttl = ttl or self.default_ttl
            
            # Serialize value
            if serialize_json:
                try:
                    serialized = json.dumps(value, default=str)
                except (TypeError, ValueError):
                    # Fallback to pickle for complex objects
                    serialized = pickle.dumps(value)
            else:
                serialized = pickle.dumps(value)
            
            await self.redis.setex(key, ttl, serialized)
            logger.debug("Cache set", key=key, ttl=ttl)
            return True
            
        except Exception as e:
            logger.error("Cache set failed", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """
        Delete key from cache
        
        Args:
            key: Cache key to delete
            
        Returns:
            True if key was deleted, False otherwise
        """
        try:
            result = await self.redis.delete(key)
            logger.debug("Cache delete", key=key, deleted=bool(result))
            return bool(result)
        except Exception as e:
            logger.error("Cache delete failed", key=key, error=str(e))
            return False
    
    async def exists(self, key: str) -> bool:
        """
        Check if key exists in cache
        
        Args:
            key: Cache key to check
            
        Returns:
            True if key exists, False otherwise
        """
        try:
            return bool(await self.redis.exists(key))
        except Exception as e:
            logger.error("Cache exists check failed", key=key, error=str(e))
            return False
    
    async def expire(self, key: str, ttl: int) -> bool:
        """
        Set expiration time for existing key
        
        Args:
            key: Cache key
            ttl: Time to live in seconds
            
        Returns:
            True if successful, False otherwise
        """
        try:
            result = await self.redis.expire(key, ttl)
            logger.debug("Cache expire set", key=key, ttl=ttl)
            return bool(result)
        except Exception as e:
            logger.error("Cache expire failed", key=key, error=str(e))
            return False
    
    async def get_many(self, keys: List[str]) -> Dict[str, Any]:
        """
        Get multiple values from cache
        
        Args:
            keys: List of cache keys
            
        Returns:
            Dictionary of key-value pairs
        """
        try:
            values = await self.redis.mget(keys)
            result = {}
            
            for key, value in zip(keys, values):
                if value is not None:
                    try:
                        result[key] = json.loads(value)
                    except (json.JSONDecodeError, TypeError):
                        result[key] = pickle.loads(value)
            
            logger.debug("Cache get_many", keys_requested=len(keys), keys_found=len(result))
            return result
            
        except Exception as e:
            logger.error("Cache get_many failed", keys=keys, error=str(e))
            return {}
    
    async def set_many(
        self,
        mapping: Dict[str, Any],
        ttl: Optional[int] = None,
        serialize_json: bool = True
    ) -> bool:
        """
        Set multiple values in cache
        
        Args:
            mapping: Dictionary of key-value pairs
            ttl: Time to live in seconds
            serialize_json: Whether to serialize as JSON
            
        Returns:
            True if successful, False otherwise
        """
        try:
            ttl = ttl or self.default_ttl
            pipe = self.redis.pipeline()
            
            for key, value in mapping.items():
                # Serialize value
                if serialize_json:
                    try:
                        serialized = json.dumps(value, default=str)
                    except (TypeError, ValueError):
                        serialized = pickle.dumps(value)
                else:
                    serialized = pickle.dumps(value)
                
                pipe.setex(key, ttl, serialized)
            
            await pipe.execute()
            logger.debug("Cache set_many", keys_set=len(mapping), ttl=ttl)
            return True
            
        except Exception as e:
            logger.error("Cache set_many failed", keys=list(mapping.keys()), error=str(e))
            return False
    
    async def delete_pattern(self, pattern: str) -> int:
        """
        Delete all keys matching pattern
        
        Args:
            pattern: Redis pattern (e.g., "user:*", "token:eth:*")
            
        Returns:
            Number of keys deleted
        """
        try:
            keys = []
            async for key in self.redis.scan_iter(match=pattern):
                keys.append(key)
            
            if keys:
                deleted = await self.redis.delete(*keys)
                logger.info("Cache pattern delete", pattern=pattern, deleted=deleted)
                return deleted
            
            return 0
            
        except Exception as e:
            logger.error("Cache pattern delete failed", pattern=pattern, error=str(e))
            return 0
    
    async def increment(self, key: str, amount: int = 1, ttl: Optional[int] = None) -> int:
        """
        Increment numeric value in cache
        
        Args:
            key: Cache key
            amount: Amount to increment by
            ttl: TTL for new keys
            
        Returns:
            New value after increment
        """
        try:
            pipe = self.redis.pipeline()
            pipe.incrby(key, amount)
            
            if ttl:
                pipe.expire(key, ttl)
            
            results = await pipe.execute()
            new_value = results[0]
            
            logger.debug("Cache increment", key=key, amount=amount, new_value=new_value)
            return new_value
            
        except Exception as e:
            logger.error("Cache increment failed", key=key, error=str(e))
            return 0
    
    async def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics
        
        Returns:
            Dictionary with cache stats
        """
        try:
            info = await self.redis.info()
            return {
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory", 0),
                "used_memory_human": info.get("used_memory_human", "0B"),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "total_commands_processed": info.get("total_commands_processed", 0),
            }
        except Exception as e:
            logger.error("Cache stats failed", error=str(e))
            return {}
    
    async def health_check(self) -> bool:
        """
        Check if Redis is healthy
        
        Returns:
            True if healthy, False otherwise
        """
        try:
            await self.redis.ping()
            return True
        except Exception as e:
            logger.error("Cache health check failed", error=str(e))
            return False
    
    async def close(self):
        """Close Redis connection"""
        try:
            await self.redis.close()
            logger.info("Cache connection closed")
        except Exception as e:
            logger.error("Cache close failed", error=str(e))


# Cache key builders
class CacheKeys:
    """Cache key builders for consistent naming"""
    
    @staticmethod
    def token(chain: str, address: str) -> str:
        """Token cache key"""
        return f"token:{chain}:{address.lower()}"
    
    @staticmethod
    def price_data(chain: str, address: str) -> str:
        """Price data cache key"""
        return f"price:{chain}:{address.lower()}"
    
    @staticmethod
    def rug_analysis(chain: str, address: str) -> str:
        """Rug analysis cache key"""
        return f"rug_analysis:{chain}:{address.lower()}"
    
    @staticmethod
    def user_session(user_id: str) -> str:
        """User session cache key"""
        return f"session:{user_id}"
    
    @staticmethod
    def rate_limit(identifier: str, window: str) -> str:
        """Rate limit cache key"""
        return f"rate_limit:{identifier}:{window}"
    
    @staticmethod
    def api_response(service: str, endpoint: str, params_hash: str) -> str:
        """API response cache key"""
        return f"api:{service}:{endpoint}:{params_hash}"


# Global cache manager instance
cache_manager = CacheManager()
