"""
OpenTelemetry observability setup and utilities
"""

import os
from typing import Optional

try:
    from opentelemetry import trace, metrics
    from opentelemetry.exporter.jaeger.thrift import <PERSON><PERSON><PERSON>Exporter
    from opentelemetry.exporter.prometheus import PrometheusMetricReader
    from opentelemetry.instrumentation.fastapi import <PERSON><PERSON><PERSON><PERSON>rumentor
    from opentelemetry.instrumentation.requests import RequestsInstrumentor
    from opentelemetry.instrumentation.redis import RedisInstrumentor
    from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
    from opentelemetry.sdk.trace import TracerProvider
    from opentelemetry.sdk.trace.export import BatchSpanProcessor
    from opentelemetry.sdk.metrics import MeterProvider
    from opentelemetry.sdk.resources import Resource
    OTEL_AVAILABLE = True
except ImportError:
    OTEL_AVAILABLE = False

try:
    from prometheus_client import start_http_server
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False

from shared.config.settings import settings


class DummyMetric:
    """Dummy metric class for when OpenTelemetry is not available"""

    def add(self, amount, attributes=None):
        """Dummy add method"""
        pass

    def record(self, amount, attributes=None):
        """Dummy record method"""
        pass


def setup_observability() -> None:
    """
    Set up OpenTelemetry tracing and metrics for the application
    """
    if not OTEL_AVAILABLE:
        print("⚠️  OpenTelemetry not available - observability disabled")
        return

    # Create resource with service information
    resource = Resource.create({
        "service.name": settings.observability.otel_service_name,
        "service.version": "1.0.0",
        "service.environment": settings.app.environment,
    })

    # Set up tracing
    setup_tracing(resource)

    # Set up metrics
    setup_metrics(resource)

    # Auto-instrument common libraries
    setup_auto_instrumentation()

    print(f"✓ OpenTelemetry observability configured for {settings.observability.otel_service_name}")


def setup_tracing(resource) -> None:
    """Set up distributed tracing with Jaeger"""
    if not OTEL_AVAILABLE:
        return

    # Create tracer provider
    tracer_provider = TracerProvider(resource=resource)
    trace.set_tracer_provider(tracer_provider)

    # Configure Jaeger exporter
    jaeger_exporter = JaegerExporter(
        agent_host_name="localhost",
        agent_port=14268,
        collector_endpoint="http://localhost:14268/api/traces",
    )

    # Add span processor
    span_processor = BatchSpanProcessor(jaeger_exporter)
    tracer_provider.add_span_processor(span_processor)


def setup_metrics(resource) -> None:
    """Set up metrics collection with Prometheus"""
    if not OTEL_AVAILABLE or not PROMETHEUS_AVAILABLE:
        return

    # Start Prometheus metrics server
    if settings.observability.metrics_enabled:
        start_http_server(settings.observability.metrics_port)

    # Create metric reader
    prometheus_reader = PrometheusMetricReader()

    # Create meter provider
    meter_provider = MeterProvider(
        resource=resource,
        metric_readers=[prometheus_reader]
    )
    metrics.set_meter_provider(meter_provider)


def setup_auto_instrumentation() -> None:
    """Set up automatic instrumentation for common libraries"""
    if not OTEL_AVAILABLE:
        return

    # Instrument FastAPI
    FastAPIInstrumentor.instrument()

    # Instrument HTTP requests
    RequestsInstrumentor().instrument()

    # Instrument Redis
    RedisInstrumentor().instrument()

    # Instrument SQLAlchemy
    SQLAlchemyInstrumentor().instrument()


def get_tracer(name: str):
    """
    Get a tracer instance for the given name

    Args:
        name: Name of the tracer (usually module name)

    Returns:
        Tracer instance or None if not available
    """
    if OTEL_AVAILABLE:
        return trace.get_tracer(name)
    return None


def get_meter(name: str):
    """
    Get a meter instance for the given name

    Args:
        name: Name of the meter (usually module name)

    Returns:
        Meter instance or None if not available
    """
    if OTEL_AVAILABLE:
        return metrics.get_meter(name)
    return None


class TracingMixin:
    """Mixin class to add tracing capabilities to any class"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._tracer = get_tracer(self.__class__.__module__)
    
    def trace_method(self, method_name: str, **attributes):
        """
        Decorator to trace method calls
        
        Args:
            method_name: Name of the method being traced
            **attributes: Additional attributes to add to the span
        """
        def decorator(func):
            def wrapper(*args, **kwargs):
                with self._tracer.start_as_current_span(
                    f"{self.__class__.__name__}.{method_name}",
                    attributes=attributes
                ) as span:
                    try:
                        result = func(*args, **kwargs)
                        span.set_attribute("success", True)
                        return result
                    except Exception as e:
                        span.set_attribute("success", False)
                        span.set_attribute("error.type", type(e).__name__)
                        span.set_attribute("error.message", str(e))
                        raise
            return wrapper
        return decorator


class MetricsMixin:
    """Mixin class to add metrics capabilities to any class"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._meter = get_meter(self.__class__.__module__)
        self._setup_metrics()
    
    def _setup_metrics(self):
        """Set up common metrics for the class"""
        self.request_counter = self._meter.create_counter(
            name=f"{self.__class__.__name__.lower()}_requests_total",
            description=f"Total number of requests to {self.__class__.__name__}",
            unit="1"
        )
        
        self.request_duration = self._meter.create_histogram(
            name=f"{self.__class__.__name__.lower()}_request_duration_seconds",
            description=f"Request duration for {self.__class__.__name__}",
            unit="s"
        )
        
        self.error_counter = self._meter.create_counter(
            name=f"{self.__class__.__name__.lower()}_errors_total",
            description=f"Total number of errors in {self.__class__.__name__}",
            unit="1"
        )


class ObservabilityManager:
    """Manager class for observability operations"""

    def __init__(self):
        self.tracer = get_tracer(__name__)
        self.meter = get_meter(__name__)
        self._setup_metrics()

    def _setup_metrics(self):
        """Set up application-wide metrics"""
        if not self.meter:
            # Create dummy metrics if OpenTelemetry is not available
            self.app_requests = DummyMetric()
            self.app_errors = DummyMetric()
            self.active_connections = DummyMetric()
            return

        self.app_requests = self.meter.create_counter(
            name="app_requests_total",
            description="Total application requests",
            unit="1"
        )

        self.app_errors = self.meter.create_counter(
            name="app_errors_total",
            description="Total application errors",
            unit="1"
        )

        self.active_connections = self.meter.create_up_down_counter(
            name="app_active_connections",
            description="Number of active connections",
            unit="1"
        )
    
    def record_request(self, endpoint: str, method: str, status_code: int):
        """Record a request metric"""
        self.app_requests.add(1, {
            "endpoint": endpoint,
            "method": method,
            "status_code": str(status_code)
        })
    
    def record_error(self, error_type: str, endpoint: str):
        """Record an error metric"""
        self.app_errors.add(1, {
            "error_type": error_type,
            "endpoint": endpoint
        })
    
    def increment_connections(self):
        """Increment active connections"""
        self.active_connections.add(1)
    
    def decrement_connections(self):
        """Decrement active connections"""
        self.active_connections.add(-1)


# Global observability manager instance
observability_manager = ObservabilityManager()
