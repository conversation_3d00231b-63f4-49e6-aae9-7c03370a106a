"""
Validation utilities
"""

import re
from typing import Optional

from shared.models.token import ChainType


def validate_contract_address(address: str) -> bool:
    """
    Validate Ethereum-style contract address
    
    Args:
        address: Contract address to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not isinstance(address, str):
        return False
    
    # Check format: 0x followed by 40 hex characters
    pattern = r'^0x[a-fA-F0-9]{40}$'
    return bool(re.match(pattern, address))


def validate_chain(chain: str) -> bool:
    """
    Validate blockchain chain identifier
    
    Args:
        chain: Chain identifier to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        ChainType(chain)
        return True
    except ValueError:
        return False


def validate_email(email: str) -> bool:
    """
    Validate email address format
    
    Args:
        email: Email address to validate
        
    Returns:
        True if valid, False otherwise
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_token_symbol(symbol: str) -> bool:
    """
    Validate token symbol format
    
    Args:
        symbol: Token symbol to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not isinstance(symbol, str):
        return False
    
    # Symbol should be 1-20 characters, alphanumeric
    return 1 <= len(symbol) <= 20 and symbol.isalnum()


def sanitize_string(value: str, max_length: Optional[int] = None) -> str:
    """
    Sanitize string input
    
    Args:
        value: String to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized string
    """
    if not isinstance(value, str):
        return ""
    
    # Remove control characters and excessive whitespace
    sanitized = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', value)
    sanitized = ' '.join(sanitized.split())
    
    # Truncate if necessary
    if max_length and len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    return sanitized
