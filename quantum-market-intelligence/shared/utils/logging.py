"""
Structured logging utilities using structlog
"""

import logging
import sys
from typing import Any, Dict, Optional

import structlog
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from shared.config.settings import settings


def setup_logging() -> None:
    """
    Set up structured logging with appropriate configuration for the environment
    """
    # Configure structlog
    processors = [
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        _get_renderer(),
    ]

    # Add logger name processor if available
    if hasattr(structlog.processors, 'add_logger_name'):
        processors.insert(2, structlog.processors.add_logger_name)

    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, settings.observability.log_level.upper())
        ),
        logger_factory=structlog.PrintLoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        level=getattr(logging, settings.observability.log_level.upper()),
        handlers=[_get_handler()],
    )
    
    # Set specific logger levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    
    print(f"✓ Logging configured with level: {settings.observability.log_level.upper()}")


def _get_renderer():
    """Get the appropriate log renderer based on configuration"""
    if settings.observability.log_format == "json":
        return structlog.processors.JSONRenderer()
    else:
        return structlog.dev.ConsoleRenderer(colors=True)


def _get_handler():
    """Get the appropriate log handler based on configuration"""
    if settings.app.is_development:
        console = Console(stderr=True)
        return RichHandler(
            console=console,
            show_time=True,
            show_level=True,
            show_path=True,
            markup=True,
            rich_tracebacks=True,
        )
    else:
        return logging.StreamHandler(sys.stdout)


def get_logger(name: str) -> structlog.BoundLogger:
    """
    Get a structured logger instance
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Configured structlog logger
    """
    return structlog.get_logger(name)


class LoggerMixin:
    """Mixin to add logging capabilities to any class"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.logger = get_logger(self.__class__.__module__)
    
    def log_info(self, message: str, **kwargs):
        """Log info message with context"""
        self.logger.info(message, class_name=self.__class__.__name__, **kwargs)
    
    def log_warning(self, message: str, **kwargs):
        """Log warning message with context"""
        self.logger.warning(message, class_name=self.__class__.__name__, **kwargs)
    
    def log_error(self, message: str, **kwargs):
        """Log error message with context"""
        self.logger.error(message, class_name=self.__class__.__name__, **kwargs)
    
    def log_debug(self, message: str, **kwargs):
        """Log debug message with context"""
        self.logger.debug(message, class_name=self.__class__.__name__, **kwargs)


class RequestLogger:
    """Logger for HTTP requests with structured context"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def log_request(
        self,
        method: str,
        path: str,
        status_code: int,
        duration_ms: float,
        user_id: Optional[str] = None,
        request_id: Optional[str] = None,
        **extra_context
    ):
        """
        Log HTTP request with structured data
        
        Args:
            method: HTTP method
            path: Request path
            status_code: Response status code
            duration_ms: Request duration in milliseconds
            user_id: Optional user ID
            request_id: Optional request ID
            **extra_context: Additional context to log
        """
        context = {
            "method": method,
            "path": path,
            "status_code": status_code,
            "duration_ms": duration_ms,
            "request_type": "http_request",
        }
        
        if user_id:
            context["user_id"] = user_id
        if request_id:
            context["request_id"] = request_id
        
        context.update(extra_context)
        
        # Choose log level based on status code
        if status_code >= 500:
            self.logger.error("HTTP request completed", **context)
        elif status_code >= 400:
            self.logger.warning("HTTP request completed", **context)
        else:
            self.logger.info("HTTP request completed", **context)


class DatabaseLogger:
    """Logger for database operations"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def log_query(
        self,
        operation: str,
        table: str,
        duration_ms: float,
        rows_affected: Optional[int] = None,
        **extra_context
    ):
        """
        Log database query with structured data
        
        Args:
            operation: Database operation (SELECT, INSERT, UPDATE, DELETE)
            table: Table name
            duration_ms: Query duration in milliseconds
            rows_affected: Number of rows affected
            **extra_context: Additional context
        """
        context = {
            "operation": operation,
            "table": table,
            "duration_ms": duration_ms,
            "query_type": "database_query",
        }
        
        if rows_affected is not None:
            context["rows_affected"] = rows_affected
        
        context.update(extra_context)
        
        if duration_ms > 1000:  # Slow query threshold
            self.logger.warning("Slow database query", **context)
        else:
            self.logger.debug("Database query completed", **context)


class APILogger:
    """Logger for external API calls"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def log_api_call(
        self,
        service: str,
        endpoint: str,
        method: str,
        status_code: int,
        duration_ms: float,
        rate_limit_remaining: Optional[int] = None,
        **extra_context
    ):
        """
        Log external API call with structured data
        
        Args:
            service: API service name (e.g., 'coingecko', 'etherscan')
            endpoint: API endpoint
            method: HTTP method
            status_code: Response status code
            duration_ms: Request duration in milliseconds
            rate_limit_remaining: Remaining rate limit
            **extra_context: Additional context
        """
        context = {
            "service": service,
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "duration_ms": duration_ms,
            "call_type": "external_api",
        }
        
        if rate_limit_remaining is not None:
            context["rate_limit_remaining"] = rate_limit_remaining
        
        context.update(extra_context)
        
        # Log level based on status and rate limits
        if status_code >= 500:
            self.logger.error("External API call failed", **context)
        elif status_code == 429:  # Rate limited
            self.logger.warning("External API rate limited", **context)
        elif rate_limit_remaining is not None and rate_limit_remaining < 10:
            self.logger.warning("External API rate limit low", **context)
        else:
            self.logger.info("External API call completed", **context)


class SecurityLogger:
    """Logger for security-related events"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def log_auth_event(
        self,
        event_type: str,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        success: bool = True,
        **extra_context
    ):
        """
        Log authentication/authorization event
        
        Args:
            event_type: Type of auth event (login, logout, token_refresh, etc.)
            user_id: User ID if available
            ip_address: Client IP address
            user_agent: Client user agent
            success: Whether the event was successful
            **extra_context: Additional context
        """
        context = {
            "event_type": event_type,
            "success": success,
            "security_event": True,
        }
        
        if user_id:
            context["user_id"] = user_id
        if ip_address:
            context["ip_address"] = ip_address
        if user_agent:
            context["user_agent"] = user_agent
        
        context.update(extra_context)
        
        if success:
            self.logger.info("Security event", **context)
        else:
            self.logger.warning("Security event failed", **context)


# Global logger instances
request_logger = RequestLogger()
database_logger = DatabaseLogger()
api_logger = APILogger()
security_logger = SecurityLogger()
