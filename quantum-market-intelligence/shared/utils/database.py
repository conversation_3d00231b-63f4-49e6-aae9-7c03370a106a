"""
Database management utilities
"""

from typing import Optional, AsyncGenerator
import asyncpg
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from shared.config.settings import settings
from shared.utils.logging import get_logger

logger = get_logger(__name__)


class DatabaseManager:
    """Database connection and session management"""
    
    def __init__(self):
        self.engine = None
        self.session_factory = None
        self._pool = None
    
    async def initialize(self):
        """Initialize database connections"""
        try:
            # Create SQLAlchemy engine
            self.engine = create_async_engine(
                settings.database.postgres_url.replace("postgresql://", "postgresql+asyncpg://"),
                echo=settings.app.debug,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600,
            )
            
            # Create session factory
            self.session_factory = async_sessionmaker(
                self.engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # Create asyncpg connection pool
            self._pool = await asyncpg.create_pool(
                settings.database.postgres_url,
                min_size=5,
                max_size=20,
                command_timeout=60,
            )
            
            logger.info("Database connections initialized")
            
        except Exception as e:
            logger.error("Failed to initialize database", error=str(e))
            raise
    
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get SQLAlchemy session"""
        if not self.session_factory:
            await self.initialize()
        
        async with self.session_factory() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def get_connection(self):
        """Get raw asyncpg connection"""
        if not self._pool:
            await self.initialize()
        
        return await self._pool.acquire()
    
    async def release_connection(self, connection):
        """Release asyncpg connection back to pool"""
        if self._pool:
            await self._pool.release(connection)
    
    async def health_check(self) -> bool:
        """Check database health"""
        try:
            if self._pool:
                async with self._pool.acquire() as conn:
                    await conn.fetchval("SELECT 1")
                return True
            return False
        except Exception as e:
            logger.error("Database health check failed", error=str(e))
            return False
    
    async def close(self):
        """Close all database connections"""
        try:
            if self._pool:
                await self._pool.close()
            if self.engine:
                await self.engine.dispose()
            logger.info("Database connections closed")
        except Exception as e:
            logger.error("Error closing database connections", error=str(e))


# Global database manager instance
database_manager = DatabaseManager()
