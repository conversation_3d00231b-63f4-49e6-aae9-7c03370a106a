"""
Quantum Market Intelligence - Configuration Management
Production-ready settings with environment-based configuration
"""

import os
from typing import List, Optional, Dict, Any
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """Database configuration settings"""

    # PostgreSQL
    postgres_host: str = Field(default="localhost", env="POSTGRES_HOST")
    postgres_port: int = Field(default=5433, env="POSTGRES_PORT")
    postgres_db: str = Field(default="quantum_market_intelligence", env="POSTGRES_DB")
    postgres_user: str = Field(default="qmi_user", env="POSTGRES_USER")
    postgres_password: str = Field(default="qmi_password", env="POSTGRES_PASSWORD")

    model_config = {"env_file": ".env", "env_file_encoding": "utf-8", "case_sensitive": False, "extra": "ignore"}
    
    # Redis
    redis_host: str = Field(default="localhost", env="REDIS_HOST")
    redis_port: int = Field(default=6382, env="REDIS_PORT")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    redis_db: int = Field(default=0, env="REDIS_DB")
    
    # ClickHouse
    clickhouse_host: str = Field(default="localhost", env="CLICKHOUSE_HOST")
    clickhouse_port: int = Field(default=8123, env="CLICKHOUSE_PORT")
    clickhouse_db: str = Field(default="market_data", env="CLICKHOUSE_DB")
    clickhouse_user: str = Field(default="qmi_user", env="CLICKHOUSE_USER")
    clickhouse_password: str = Field(default="qmi_password", env="CLICKHOUSE_PASSWORD")
    
    @property
    def postgres_url(self) -> str:
        return f"postgresql://{self.postgres_user}:{self.postgres_password}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"
    
    @property
    def redis_url(self) -> str:
        auth = f":{self.redis_password}@" if self.redis_password else ""
        return f"redis://{auth}{self.redis_host}:{self.redis_port}/{self.redis_db}"
    
    @property
    def clickhouse_url(self) -> str:
        return f"http://{self.clickhouse_user}:{self.clickhouse_password}@{self.clickhouse_host}:{self.clickhouse_port}/{self.clickhouse_db}"


class APISettings(BaseSettings):
    """External API configuration settings"""

    # Crypto APIs
    coingecko_api_key: Optional[str] = Field(default=None, env="COINGECKO_API_KEY")
    dexscreener_api_key: Optional[str] = Field(default=None, env="DEXSCREENER_API_KEY")
    etherscan_api_key: Optional[str] = Field(default=None, env="ETHERSCAN_API_KEY")
    coin_api_key: Optional[str] = Field(default=None, env="COIN_API_KEY")
    dune_api_key: Optional[str] = Field(default=None, env="DUNE_API_KEY")
    birdeye_api_key: Optional[str] = Field(default=None, env="API_BIRDEYE_API_KEY")
    
    # Blockchain RPC URLs
    ethereum_rpc_url: str = Field(default="https://cloudflare-eth.com", env="ETHEREUM_RPC_URL")
    polygon_rpc_url: str = Field(default="https://polygon-rpc.com", env="POLYGON_RPC_URL")
    bsc_rpc_url: str = Field(default="https://bsc-dataseed.binance.org/", env="BSC_RPC_URL")
    web3_provider_url: Optional[str] = Field(default=None, env="WEB3_PROVIDER_URL")
    infura_api_key: Optional[str] = Field(default=None, env="INFURA_API_KEY")
    
    # Social Media APIs
    reddit_client_id: Optional[str] = Field(default=None, env="REDDIT_CLIENT_ID")
    reddit_client_secret: Optional[str] = Field(default=None, env="REDDIT_CLIENT_SECRET")
    twitter_bearer_token: Optional[str] = Field(default=None, env="TWITTER_BEARER_TOKEN")
    
    # Web Scraping
    scrapingbee_api_key: Optional[str] = Field(default=None, env="SCRAPINGBEE_API_KEY")
    hyperbrowser_api_key: Optional[str] = Field(default=None, env="HYPERBROWSER_API_KEY")
    
    # AI/ML APIs
    openrouter_api_key: Optional[str] = Field(default=None, env="OPENROUTER_API_KEY")
    
    # Rate Limiting
    api_rate_limit_per_minute: int = Field(default=60, env="API_RATE_LIMIT_PER_MINUTE")
    max_concurrent_requests: int = Field(default=10, env="MAX_CONCURRENT_ANALYSES")

    model_config = {"env_file": ".env", "env_file_encoding": "utf-8", "case_sensitive": False, "extra": "ignore"}


class SecuritySettings(BaseSettings):
    """Security configuration settings"""

    jwt_secret_key: str = Field(default="dev_jwt_secret_key_12345_very_long_secret_for_development_only_very_long_key_for_development", env="JWT_SECRET_KEY")
    encryption_key: str = Field(default="dev_encryption_key_12345_very_long_key_for_development", env="ENCRYPTION_KEY")
    
    # CORS settings
    cors_origins: List[str] = Field(default=["http://localhost:3000", "http://localhost:8000"])
    cors_allow_credentials: bool = True
    cors_allow_methods: List[str] = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    cors_allow_headers: List[str] = ["*"]
    
    @field_validator("jwt_secret_key")
    @classmethod
    def validate_jwt_secret(cls, v):
        if len(v) < 32:
            raise ValueError("JWT secret key must be at least 32 characters long")
        return v

    model_config = {"env_file": ".env", "env_file_encoding": "utf-8", "case_sensitive": False, "extra": "ignore"}


class ObservabilitySettings(BaseSettings):
    """Observability and monitoring configuration"""

    # OpenTelemetry
    otel_exporter_otlp_endpoint: str = Field(default="http://localhost:4317", env="OTEL_EXPORTER_OTLP_ENDPOINT")
    otel_service_name: str = Field(default="quantum-market-intelligence", env="OTEL_SERVICE_NAME")
    otel_resource_attributes: str = Field(
        default="service.name=quantum-market-intelligence,service.version=1.0.0",
        env="OTEL_RESOURCE_ATTRIBUTES"
    )
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")
    
    # Metrics
    metrics_enabled: bool = Field(default=True, env="METRICS_ENABLED")
    metrics_port: int = Field(default=8080, env="METRICS_PORT")
    
    # Health checks
    health_check_interval: int = Field(default=30, env="HEALTH_CHECK_INTERVAL")

    model_config = {"env_file": ".env", "env_file_encoding": "utf-8", "case_sensitive": False, "extra": "ignore"}


class ApplicationSettings(BaseSettings):
    """Main application configuration"""

    # Environment
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=True, env="DEBUG")
    
    # Server
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    workers: int = Field(default=1, env="WORKERS")
    
    # Cache settings
    cache_ttl_seconds: int = Field(default=3600, env="CACHE_TTL_SECONDS")
    
    # ML Model settings
    ml_retrain_interval_hours: int = Field(default=24, env="MODEL_RETRAIN_INTERVAL_HOURS")
    min_confidence_threshold: float = Field(default=3.0, env="MIN_CONFIDENCE_THRESHOLD")
    
    # Alert settings
    alert_cooldown_minutes: int = Field(default=15, env="ALERT_COOLDOWN_MINUTES")
    max_alerts_per_hour: int = Field(default=100, env="MAX_ALERTS_PER_HOUR")
    
    @field_validator("environment")
    @classmethod
    def validate_environment(cls, v):
        allowed_envs = ["development", "staging", "production"]
        if v not in allowed_envs:
            raise ValueError(f"Environment must be one of {allowed_envs}")
        return v
    
    @property
    def is_production(self) -> bool:
        return self.environment == "production"
    
    @property
    def is_development(self) -> bool:
        return self.environment == "development"

    model_config = {"env_file": ".env", "env_file_encoding": "utf-8", "case_sensitive": False, "extra": "ignore"}


class Settings:
    """Main settings class combining all configuration sections"""

    def __init__(self):
        # Initialize sub-settings
        self.database = DatabaseSettings()
        self.api = APISettings()
        self.security = SecuritySettings()
        self.observability = ObservabilitySettings()
        self.app = ApplicationSettings()


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance"""
    return settings


# Validation functions
def validate_api_keys() -> Dict[str, bool]:
    """Validate that required API keys are present"""
    required_keys = {
        "coingecko_api_key": settings.api.coingecko_api_key,
        "etherscan_api_key": settings.api.etherscan_api_key,
        "infura_api_key": settings.api.infura_api_key,
    }
    
    return {key: value is not None and len(value) > 0 for key, value in required_keys.items()}


def validate_database_connections() -> Dict[str, bool]:
    """Validate database connection settings"""
    return {
        "postgres": all([
            settings.database.postgres_host,
            settings.database.postgres_user,
            settings.database.postgres_password,
            settings.database.postgres_db
        ]),
        "redis": all([
            settings.database.redis_host,
            settings.database.redis_port
        ]),
        "clickhouse": all([
            settings.database.clickhouse_host,
            settings.database.clickhouse_user,
            settings.database.clickhouse_password
        ])
    }


if __name__ == "__main__":
    # Configuration validation
    print("=== Quantum Market Intelligence Configuration ===")
    print(f"Environment: {settings.app.environment}")
    print(f"Debug Mode: {settings.app.debug}")
    print(f"Database URL: {settings.database.postgres_url}")
    print(f"Redis URL: {settings.database.redis_url}")
    
    # Validate API keys
    api_validation = validate_api_keys()
    print("\n=== API Key Validation ===")
    for key, valid in api_validation.items():
        status = "✓" if valid else "✗"
        print(f"{status} {key}: {'Valid' if valid else 'Missing'}")
    
    # Validate database connections
    db_validation = validate_database_connections()
    print("\n=== Database Configuration Validation ===")
    for db, valid in db_validation.items():
        status = "✓" if valid else "✗"
        print(f"{status} {db}: {'Valid' if valid else 'Invalid'}")
