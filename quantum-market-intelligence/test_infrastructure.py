#!/usr/bin/env python3
"""
Test script to verify infrastructure components are working
"""

import asyncio
import sys
import os
from datetime import datetime

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_database_connection():
    """Test PostgreSQL connection"""
    try:
        import asyncpg
        conn = await asyncpg.connect('postgresql://qmi_user:qmi_password@localhost:5433/quantum_market_intelligence')
        result = await conn.fetchval('SELECT version()')
        await conn.close()
        print('✓ PostgreSQL connection successful')
        print(f'  Version: {result[:50]}...')
        return True
    except Exception as e:
        print(f'✗ PostgreSQL connection failed: {e}')
        return False

async def test_redis_connection():
    """Test Redis connection"""
    try:
        import redis.asyncio as redis
        r = redis.from_url('redis://localhost:6382/0')
        await r.ping()
        await r.close()
        print('✓ Redis connection successful')
        return True
    except Exception as e:
        print(f'✗ Redis connection failed: {e}')
        return False

async def test_clickhouse_connection():
    """Test ClickHouse connection"""
    try:
        import aiohttp
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8123/ping') as response:
                if response.status == 200:
                    print('✓ ClickHouse connection successful')
                    return True
                else:
                    print(f'✗ ClickHouse connection failed: HTTP {response.status}')
                    return False
    except Exception as e:
        print(f'✗ ClickHouse connection failed: {e}')
        return False

def test_monitoring_services():
    """Test monitoring services"""
    import requests
    
    services = {
        'Jaeger': 'http://localhost:16686',
        'Prometheus': 'http://localhost:9091',
        'Grafana': 'http://localhost:3002'
    }
    
    results = {}
    for name, url in services.items():
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f'✓ {name} is accessible at {url}')
                results[name] = True
            else:
                print(f'✗ {name} returned HTTP {response.status_code}')
                results[name] = False
        except Exception as e:
            print(f'✗ {name} connection failed: {e}')
            results[name] = False
    
    return results

def test_configuration():
    """Test configuration loading"""
    try:
        from shared.config.settings import settings
        print('✓ Configuration loaded successfully')
        print(f'  Environment: {settings.app.environment}')
        print(f'  Debug mode: {settings.app.debug}')
        print(f'  Database URL: {settings.database.postgres_url}')
        print(f'  Redis URL: {settings.database.redis_url}')
        return True
    except Exception as e:
        print(f'✗ Configuration loading failed: {e}')
        return False

async def test_cache_operations():
    """Test cache operations"""
    try:
        from shared.utils.cache import cache_manager
        
        # Test basic operations
        test_key = "test:infrastructure"
        test_value = {"timestamp": datetime.utcnow().isoformat(), "test": True}
        
        # Set value
        success = await cache_manager.set(test_key, test_value, ttl=60)
        if not success:
            print('✗ Cache set operation failed')
            return False
        
        # Get value
        retrieved = await cache_manager.get(test_key)
        if retrieved != test_value:
            print('✗ Cache get operation failed')
            return False
        
        # Delete value
        deleted = await cache_manager.delete(test_key)
        if not deleted:
            print('✗ Cache delete operation failed')
            return False
        
        print('✓ Cache operations successful')
        return True
        
    except Exception as e:
        print(f'✗ Cache operations failed: {e}')
        return False

async def main():
    """Run all infrastructure tests"""
    print("=" * 60)
    print("QUANTUM MARKET INTELLIGENCE - INFRASTRUCTURE TEST")
    print("=" * 60)
    print(f"Test started at: {datetime.utcnow().isoformat()}")
    print()
    
    results = {}
    
    # Test configuration
    print("1. Testing Configuration...")
    results['config'] = test_configuration()
    print()
    
    # Test database connections
    print("2. Testing Database Connections...")
    results['postgres'] = await test_database_connection()
    results['redis'] = await test_redis_connection()
    results['clickhouse'] = await test_clickhouse_connection()
    print()
    
    # Test cache operations
    print("3. Testing Cache Operations...")
    results['cache_ops'] = await test_cache_operations()
    print()
    
    # Test monitoring services
    print("4. Testing Monitoring Services...")
    monitoring_results = test_monitoring_services()
    results.update(monitoring_results)
    print()
    
    # Summary
    print("=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for component, status in results.items():
        status_icon = "✓" if status else "✗"
        print(f"{status_icon} {component}")
    
    print()
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All infrastructure components are working correctly!")
        return 0
    else:
        print("⚠️  Some infrastructure components need attention.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
