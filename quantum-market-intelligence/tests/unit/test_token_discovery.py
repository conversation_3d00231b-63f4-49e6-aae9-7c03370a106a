"""
Unit tests for token discovery service
"""

import pytest
import asyncio
from unittest.mock import Async<PERSON><PERSON>, MagicMock, patch
from datetime import datetime, timedelta

from services.data_ingestion.token_discovery import (
    TokenDiscoveryService, 
    DataSource, 
    TokenDiscoveryResult
)
from services.data_ingestion.rate_limiter import APIRateLimiter
from services.data_ingestion.data_validator import DataValidator


class TestTokenDiscoveryService:
    """Test cases for TokenDiscoveryService"""
    
    @pytest.fixture
    async def discovery_service(self):
        """Create a TokenDiscoveryService instance for testing"""
        async with TokenDiscoveryService() as service:
            yield service
    
    @pytest.fixture
    def mock_session(self):
        """Mock aiohttp session"""
        session = AsyncMock()
        response = AsyncMock()
        response.status = 200
        response.json = AsyncMock()
        session.get.return_value.__aenter__.return_value = response
        return session, response
    
    @pytest.mark.asyncio
    async def test_discover_new_tokens_success(self, discovery_service, mock_session):
        """Test successful token discovery"""
        session, response = mock_session
        discovery_service.session = session
        
        # Mock DexScreener response
        response.json.return_value = [
            {
                "tokenAddress": "******************************************",
                "chainId": "ethereum",
                "amount": 1000,
                "description": "Test token"
            }
        ]
        
        # Mock rate limiter
        discovery_service.rate_limiter.acquire = AsyncMock(return_value=True)
        
        tokens = await discovery_service.discover_new_tokens(
            max_age_hours=24,
            min_liquidity_usd=20000,
            min_volume_usd=100000
        )
        
        assert len(tokens) >= 0  # Should return some tokens or empty list
        session.get.assert_called()
    
    @pytest.mark.asyncio
    async def test_discover_dexscreener_rate_limited(self, discovery_service):
        """Test DexScreener discovery with rate limiting"""
        # Mock rate limiter to return False (rate limited)
        discovery_service.rate_limiter.acquire = AsyncMock(return_value=False)
        
        result = await discovery_service._discover_dexscreener(24)
        
        assert not result.success
        assert "Rate limit exceeded" in result.error
        assert result.source == DataSource.DEXSCREENER
    
    @pytest.mark.asyncio
    async def test_discover_dexscreener_api_error(self, discovery_service, mock_session):
        """Test DexScreener discovery with API error"""
        session, response = mock_session
        discovery_service.session = session
        response.status = 500
        
        discovery_service.rate_limiter.acquire = AsyncMock(return_value=True)
        
        result = await discovery_service._discover_dexscreener(24)
        
        assert not result.success
        assert "500" in result.error
        assert result.source == DataSource.DEXSCREENER
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_functionality(self, discovery_service):
        """Test circuit breaker opens after failures"""
        source = DataSource.DEXSCREENER
        
        # Record multiple failures
        for _ in range(5):
            discovery_service._record_failure(source, "Test error")
        
        # Circuit breaker should be open
        assert discovery_service._is_circuit_breaker_open(source)
        
        # Reset circuit breaker
        discovery_service._reset_circuit_breaker(source)
        
        # Circuit breaker should be closed
        assert not discovery_service._is_circuit_breaker_open(source)
    
    def test_deduplicate_tokens(self, discovery_service):
        """Test token deduplication"""
        tokens = [
            {"contract_address": "0x1234", "chain": "ethereum", "symbol": "TEST1"},
            {"contract_address": "0x1234", "chain": "ethereum", "symbol": "TEST2"},  # Duplicate
            {"contract_address": "0x5678", "chain": "ethereum", "symbol": "TEST3"},
            {"contract_address": "0x1234", "chain": "bsc", "symbol": "TEST4"},  # Different chain
        ]
        
        unique_tokens = discovery_service._deduplicate_tokens(tokens)
        
        assert len(unique_tokens) == 3  # Should remove one duplicate
        addresses = [t["contract_address"] for t in unique_tokens]
        assert addresses.count("0x1234") == 2  # One for each chain
    
    def test_map_chain_id(self, discovery_service):
        """Test chain ID mapping"""
        assert discovery_service._map_chain_id("ethereum") == "ethereum"
        assert discovery_service._map_chain_id("bsc") == "bsc"
        assert discovery_service._map_chain_id("unknown") == "ethereum"  # Default
    
    @pytest.mark.asyncio
    async def test_apply_filters(self, discovery_service):
        """Test token filtering"""
        # Mock data validator
        discovery_service.data_validator.validate_token_data = MagicMock(return_value=True)
        
        tokens = [
            {
                "contract_address": "0x1234",
                "chain": "ethereum",
                "metadata": {"liquidity_usd": 50000, "volume_24h_usd": 200000}
            },
            {
                "contract_address": "0x5678", 
                "chain": "ethereum",
                "metadata": {"liquidity_usd": 10000, "volume_24h_usd": 50000}  # Below threshold
            }
        ]
        
        filtered = await discovery_service._apply_filters(
            tokens, 24, 20000, 100000
        )
        
        assert len(filtered) == 1
        assert filtered[0]["contract_address"] == "0x1234"


class TestAPIRateLimiter:
    """Test cases for APIRateLimiter"""
    
    @pytest.fixture
    def rate_limiter(self):
        """Create APIRateLimiter instance"""
        return APIRateLimiter()
    
    @pytest.mark.asyncio
    async def test_acquire_token_bucket_success(self, rate_limiter):
        """Test successful token acquisition"""
        result = await rate_limiter.acquire("dexscreener", 1)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_acquire_token_bucket_exhausted(self, rate_limiter):
        """Test token bucket exhaustion"""
        # Exhaust all tokens
        config = rate_limiter.configs["dexscreener"]
        for _ in range(config.requests_per_minute + 1):
            await rate_limiter.acquire("dexscreener", 1)
        
        # Next request should be rate limited
        result = await rate_limiter.acquire("dexscreener", 1)
        assert result is False
    
    @pytest.mark.asyncio
    async def test_record_failure_backoff(self, rate_limiter):
        """Test exponential backoff after failures"""
        api_name = "dexscreener"
        
        # Record failure
        await rate_limiter.record_failure(api_name)
        
        backoff_state = rate_limiter.backoff_states[api_name]
        assert backoff_state["consecutive_failures"] == 1
        assert backoff_state["current_backoff"] > 0
    
    @pytest.mark.asyncio
    async def test_record_success_resets_backoff(self, rate_limiter):
        """Test that success resets backoff state"""
        api_name = "dexscreener"
        
        # Record failure then success
        await rate_limiter.record_failure(api_name)
        await rate_limiter.record_success(api_name)
        
        backoff_state = rate_limiter.backoff_states[api_name]
        assert backoff_state["consecutive_failures"] == 0
        assert backoff_state["current_backoff"] == 0
    
    @pytest.mark.asyncio
    async def test_get_rate_limit_status(self, rate_limiter):
        """Test rate limit status reporting"""
        status = await rate_limiter.get_rate_limit_status("dexscreener")
        
        assert "api_name" in status
        assert "available_tokens" in status
        assert "consecutive_failures" in status
        assert status["api_name"] == "dexscreener"
    
    @pytest.mark.asyncio
    async def test_health_check(self, rate_limiter):
        """Test rate limiter health check"""
        health = await rate_limiter.health_check()
        
        assert "overall_healthy" in health
        assert "apis" in health
        assert isinstance(health["overall_healthy"], bool)


class TestDataValidator:
    """Test cases for DataValidator"""
    
    @pytest.fixture
    def validator(self):
        """Create DataValidator instance"""
        return DataValidator()
    
    def test_validate_token_data_valid(self, validator):
        """Test validation of valid token data"""
        token_data = {
            "contract_address": "******************************************",
            "chain": "ethereum",
            "symbol": "TEST",
            "name": "Test Token",
            "decimals": 18
        }
        
        assert validator.validate_token_data(token_data) is True
    
    def test_validate_token_data_missing_required(self, validator):
        """Test validation with missing required fields"""
        token_data = {
            "symbol": "TEST",
            "name": "Test Token"
        }
        
        assert validator.validate_token_data(token_data) is False
    
    def test_validate_ethereum_address_valid(self, validator):
        """Test Ethereum address validation"""
        valid_address = "******************************************"
        assert validator._validate_ethereum_address(valid_address) is True
    
    def test_validate_ethereum_address_invalid(self, validator):
        """Test invalid Ethereum address"""
        invalid_addresses = [
            "1234567890123456789012345678901234567890",  # No 0x prefix
            "0x12345",  # Too short
            "******************************************12345",  # Too long
            "0xGGGG567890123456789012345678901234567890"  # Invalid hex
        ]
        
        for address in invalid_addresses:
            assert validator._validate_ethereum_address(address) is False
    
    def test_validate_solana_address_valid(self, validator):
        """Test Solana address validation"""
        valid_address = "********************************"
        assert validator._validate_solana_address(valid_address) is True
    
    def test_validate_solana_address_invalid(self, validator):
        """Test invalid Solana address"""
        invalid_addresses = [
            "123",  # Too short
            "******************************************",  # Wrong format
            "11111111111111111111111111111110I"  # Invalid base58 character
        ]
        
        for address in invalid_addresses:
            assert validator._validate_solana_address(address) is False
    
    def test_normalize_token_data(self, validator):
        """Test token data normalization"""
        token_data = {
            "contract_address": "0xABCD1234567890123456789012345678901234567890",
            "chain": "ETHEREUM",
            "symbol": "  test  ",
            "name": "  Test Token  ",
            "decimals": "18"
        }
        
        normalized = validator.normalize_token_data(token_data)
        
        assert normalized["contract_address"] == "0xabcd1234567890123456789012345678901234567890"
        assert normalized["chain"] == "ethereum"
        assert normalized["symbol"] == "TEST"
        assert normalized["name"] == "Test Token"
        assert normalized["decimals"] == 18
        assert "quality_score" in normalized
        assert "validated_at" in normalized
    
    def test_calculate_quality_score(self, validator):
        """Test quality score calculation"""
        high_quality_token = {
            "contract_address": "******************************************",
            "chain": "ethereum",
            "symbol": "TEST",
            "name": "Test Token",
            "decimals": 18,
            "source": "coingecko",
            "metadata": {
                "liquidity_usd": 100000,
                "volume_24h_usd": 500000,
                "market_cap": 1000000,
                "price_usd": 1.0
            }
        }
        
        score = validator._calculate_quality_score(high_quality_token)
        assert 0.8 <= score <= 1.0  # Should be high quality
        
        low_quality_token = {
            "contract_address": "******************************************",
            "chain": "ethereum"
        }
        
        score = validator._calculate_quality_score(low_quality_token)
        assert 0.0 <= score <= 0.5  # Should be low quality
    
    def test_batch_validate(self, validator):
        """Test batch validation"""
        tokens = [
            {
                "contract_address": "******************************************",
                "chain": "ethereum",
                "symbol": "TEST1"
            },
            {
                "contract_address": "invalid",
                "chain": "ethereum",
                "symbol": "TEST2"
            },
            {
                "contract_address": "******************************************",
                "chain": "ethereum",
                "symbol": "TEST3"
            }
        ]
        
        valid, invalid = validator.batch_validate(tokens)
        
        assert len(valid) == 2
        assert len(invalid) == 1
        assert all("quality_score" in token for token in valid)
    
    def test_detect_duplicates(self, validator):
        """Test duplicate detection"""
        tokens = [
            {"contract_address": "0x1234", "chain": "ethereum"},
            {"contract_address": "0x1234", "chain": "ethereum"},  # Duplicate
            {"contract_address": "0x5678", "chain": "ethereum"},
            {"contract_address": "0x1234", "chain": "bsc"}  # Different chain
        ]
        
        unique = validator.detect_duplicates(tokens)
        
        assert len(unique) == 3  # Should remove one duplicate
