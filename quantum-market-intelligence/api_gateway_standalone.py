#!/usr/bin/env python3
"""
Standalone API Gateway for Quantum Market Intelligence
"""

import sys
import os
from datetime import datetime
from typing import Optional, List
from uuid import UUID, uuid4

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Import our shared modules
from shared.config.settings import settings
from shared.utils.logging import setup_logging, get_logger
from shared.utils.observability import setup_observability
from shared.utils.cache import cache_manager
from shared.models.token import ChainType, TokenStatus

# Set up logging
setup_logging()
logger = get_logger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Quantum Market Intelligence API",
    description="Production-ready cryptocurrency rug detection and market intelligence system",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.on_event("startup")
async def startup_event():
    """Application startup"""
    logger.info("Starting Quantum Market Intelligence API Gateway")
    
    # Initialize observability
    setup_observability()
    
    # Test cache connection
    cache_healthy = await cache_manager.health_check()
    logger.info("API Gateway startup complete", cache_healthy=cache_healthy)


@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown"""
    logger.info("Shutting down API Gateway")
    await cache_manager.close()


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "name": "Quantum Market Intelligence API",
        "version": "1.0.0",
        "status": "operational",
        "docs": "/docs"
    }


@app.get("/health/")
async def health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0"
    }


@app.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check with dependency status"""
    
    # Check cache health
    cache_healthy = await cache_manager.health_check()
    
    # Get cache stats
    cache_stats = await cache_manager.get_stats()
    
    # Mock other service checks
    database_health = {"status": "healthy", "response_time_ms": 5}
    external_apis_health = {
        "coingecko": {"status": "healthy", "response_time_ms": 150},
        "etherscan": {"status": "healthy", "response_time_ms": 200},
    }
    
    # Determine overall status
    overall_status = "healthy" if cache_healthy else "degraded"
    status_code = 200 if cache_healthy else 503
    
    response_data = {
        "status": overall_status,
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
        "services": {
            "database": database_health,
            "cache": {"status": "healthy" if cache_healthy else "unhealthy"},
            "external_apis": external_apis_health
        },
        "system": {
            "cache_stats": cache_stats,
        }
    }
    
    return JSONResponse(
        status_code=status_code,
        content=response_data
    )


@app.get("/api/v1/tokens/")
async def list_tokens(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    chain: Optional[ChainType] = Query(None, description="Filter by blockchain"),
    status: Optional[TokenStatus] = Query(None, description="Filter by token status"),
):
    """List tokens with filtering and pagination"""
    
    # Mock token data
    mock_tokens = [
        {
            "id": str(uuid4()),
            "contract_address": "******************************************",
            "chain": "ethereum",
            "symbol": "QMI",
            "name": "Quantum Market Intelligence",
            "decimals": 18,
            "status": "active",
            "price_usd": 1.25,
            "market_cap": 1250000000,
            "risk_score": 2.1,
            "confidence_score": 4.5,
            "created_at": datetime.utcnow().isoformat(),
        }
    ]
    
    # Apply filters
    filtered_tokens = mock_tokens
    if chain:
        filtered_tokens = [t for t in filtered_tokens if t["chain"] == chain]
    if status:
        filtered_tokens = [t for t in filtered_tokens if t["status"] == status]
    
    # Apply pagination
    total = len(filtered_tokens)
    start = (page - 1) * size
    end = start + size
    paginated_tokens = filtered_tokens[start:end]
    
    return {
        "items": paginated_tokens,
        "total": total,
        "page": page,
        "size": size,
        "pages": (total + size - 1) // size
    }


@app.get("/api/v1/tokens/{token_id}")
async def get_token(token_id: UUID):
    """Get token by ID"""
    
    # Mock token data
    return {
        "id": str(token_id),
        "contract_address": "******************************************",
        "chain": "ethereum",
        "symbol": "QMI",
        "name": "Quantum Market Intelligence",
        "decimals": 18,
        "status": "active",
        "price_usd": 1.25,
        "market_cap": 1250000000,
        "risk_score": 2.1,
        "confidence_score": 4.5,
        "created_at": datetime.utcnow().isoformat(),
    }


@app.post("/api/v1/analysis/rug-detection/{chain}/{contract_address}")
async def analyze_rug_potential(chain: ChainType, contract_address: str):
    """Analyze token for rug pull potential"""
    
    # Validate contract address
    if not contract_address.startswith("0x") or len(contract_address) != 42:
        raise HTTPException(status_code=400, detail="Invalid contract address format")
    
    logger.info("Starting rug detection analysis", chain=chain, contract_address=contract_address)
    
    # Mock analysis results
    return {
        "token": {
            "contract_address": contract_address.lower(),
            "chain": chain,
            "symbol": "MOCK",
            "name": "Mock Token"
        },
        "analysis": {
            "overall_risk_score": 2.3,
            "confidence_score": 4.2,
            "risk_level": "medium",
            "analysis_timestamp": datetime.utcnow().isoformat()
        },
        "factors": {
            "contract_analysis": {"score": 2.1},
            "liquidity_analysis": {"score": 1.8},
            "holder_analysis": {"score": 2.7},
            "social_analysis": {"score": 2.0}
        },
        "recommendations": [
            "Monitor deployer wallet activity",
            "Watch for large holder movements",
            "Verify liquidity lock details"
        ]
    }


@app.get("/api/v1/analysis/alerts")
async def get_active_alerts(
    severity: Optional[str] = Query(None, description="Filter by alert severity"),
    limit: int = Query(50, description="Maximum number of alerts to return")
):
    """Get active alerts"""
    
    # Mock alerts
    mock_alerts = [
        {
            "id": "alert-001",
            "token": {
                "contract_address": "******************************************",
                "chain": "ethereum",
                "symbol": "RISK",
                "name": "Risky Token"
            },
            "alert_type": "rug_detection",
            "severity": "high",
            "title": "High rug pull risk detected",
            "description": "Token shows multiple red flags",
            "confidence_score": 4.2,
            "created_at": datetime.utcnow().isoformat()
        }
    ]
    
    # Apply filters
    if severity:
        mock_alerts = [alert for alert in mock_alerts if alert["severity"] == severity]
    
    mock_alerts = mock_alerts[:limit]
    
    return {
        "alerts": mock_alerts,
        "total": len(mock_alerts),
        "timestamp": datetime.utcnow().isoformat()
    }


def main():
    """Main entry point"""
    logger.info(
        "Starting API Gateway",
        host=settings.app.host,
        port=settings.app.port,
        environment=settings.app.environment
    )
    
    uvicorn.run(
        app,
        host=settings.app.host,
        port=settings.app.port,
        reload=False,
        log_config=None,
        access_log=False,
    )


if __name__ == "__main__":
    main()
