#!/usr/bin/env python3
"""
Simple test script for token discovery service
"""

import asyncio
import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Import with absolute paths
try:
    from services.data_ingestion.token_discovery import TokenDiscoveryService, DataSource
    from services.data_ingestion.rate_limiter import APIRateLimiter
    from services.data_ingestion.data_validator import DataValidator
except ImportError as e:
    print(f"Import error: {e}")
    print("Trying alternative import method...")

    # Alternative import method
    import importlib.util

    # Load token_discovery module
    spec = importlib.util.spec_from_file_location(
        "token_discovery",
        os.path.join(project_root, "services", "data-ingestion", "token_discovery.py")
    )
    token_discovery = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(token_discovery)

    TokenDiscoveryService = token_discovery.TokenDiscoveryService
    DataSource = token_discovery.DataSource

    # Load rate_limiter module
    spec = importlib.util.spec_from_file_location(
        "rate_limiter",
        os.path.join(project_root, "services", "data-ingestion", "rate_limiter.py")
    )
    rate_limiter = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(rate_limiter)

    APIRateLimiter = rate_limiter.APIRateLimiter

    # Load data_validator module
    spec = importlib.util.spec_from_file_location(
        "data_validator",
        os.path.join(project_root, "services", "data-ingestion", "data_validator.py")
    )
    data_validator = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(data_validator)

    DataValidator = data_validator.DataValidator


async def test_token_discovery():
    """Test token discovery service"""
    print("🧪 Testing Token Discovery Service...")
    
    try:
        async with TokenDiscoveryService() as service:
            print("✅ TokenDiscoveryService initialized successfully")
            
            # Test circuit breaker functionality
            source = DataSource.DEXSCREENER
            print(f"🔧 Testing circuit breaker for {source}")
            
            # Initially should be closed
            assert not service._is_circuit_breaker_open(source)
            print("✅ Circuit breaker initially closed")
            
            # Record failures to open it
            for i in range(5):
                service._record_failure(source, f"Test error {i}")
            
            # Should be open now
            assert service._is_circuit_breaker_open(source)
            print("✅ Circuit breaker opened after failures")
            
            # Reset should close it
            service._reset_circuit_breaker(source)
            assert not service._is_circuit_breaker_open(source)
            print("✅ Circuit breaker reset successfully")
            
            # Test deduplication
            tokens = [
                {"contract_address": "0x1234", "chain": "ethereum", "symbol": "TEST1"},
                {"contract_address": "0x1234", "chain": "ethereum", "symbol": "TEST2"},  # Duplicate
                {"contract_address": "0x5678", "chain": "ethereum", "symbol": "TEST3"},
            ]
            
            unique_tokens = service._deduplicate_tokens(tokens)
            assert len(unique_tokens) == 2
            print("✅ Token deduplication working")
            
            # Test chain mapping
            assert service._map_chain_id("ethereum") == "ethereum"
            assert service._map_chain_id("unknown") == "ethereum"
            print("✅ Chain mapping working")
            
    except Exception as e:
        print(f"❌ Error in token discovery test: {e}")
        return False
    
    return True


def test_rate_limiter():
    """Test rate limiter"""
    print("🧪 Testing Rate Limiter...")
    
    try:
        limiter = APIRateLimiter()
        print("✅ APIRateLimiter initialized successfully")
        
        # Test configuration
        assert "dexscreener" in limiter.configs
        assert "etherscan" in limiter.configs
        print("✅ Rate limiter configurations loaded")
        
        # Test bucket initialization
        assert "dexscreener" in limiter.buckets
        bucket = limiter.buckets["dexscreener"]
        assert bucket["tokens"] > 0
        assert bucket["capacity"] > 0
        print("✅ Token buckets initialized")
        
        # Test backoff state
        assert "dexscreener" in limiter.backoff_states
        backoff = limiter.backoff_states["dexscreener"]
        assert backoff["consecutive_failures"] == 0
        print("✅ Backoff states initialized")
        
    except Exception as e:
        print(f"❌ Error in rate limiter test: {e}")
        return False
    
    return True


def test_data_validator():
    """Test data validator"""
    print("🧪 Testing Data Validator...")
    
    try:
        validator = DataValidator()
        print("✅ DataValidator initialized successfully")
        
        # Test valid token data
        valid_token = {
            "contract_address": "******************************************",
            "chain": "ethereum",
            "symbol": "TEST",
            "name": "Test Token",
            "decimals": 18
        }
        
        assert validator.validate_token_data(valid_token)
        print("✅ Valid token data validation passed")
        
        # Test invalid token data
        invalid_token = {
            "symbol": "TEST",
            "name": "Test Token"
            # Missing required fields
        }
        
        assert not validator.validate_token_data(invalid_token)
        print("✅ Invalid token data validation failed as expected")
        
        # Test Ethereum address validation
        assert validator._validate_ethereum_address("******************************************")
        assert not validator._validate_ethereum_address("invalid")
        print("✅ Ethereum address validation working")
        
        # Test Solana address validation
        assert validator._validate_solana_address("********************************")
        assert not validator._validate_solana_address("0x1234")
        print("✅ Solana address validation working")
        
        # Test normalization
        token_data = {
            "contract_address": "0xABCD1234567890123456789012345678901234567890",
            "chain": "ETHEREUM",
            "symbol": "  test  ",
            "name": "  Test Token  ",
            "decimals": "18"
        }
        
        normalized = validator.normalize_token_data(token_data)
        assert normalized["contract_address"] == "0xabcd1234567890123456789012345678901234567890"
        assert normalized["chain"] == "ethereum"
        assert normalized["symbol"] == "TEST"
        assert normalized["name"] == "Test Token"
        assert normalized["decimals"] == 18
        print("✅ Token data normalization working")
        
        # Test quality score calculation
        score = validator._calculate_quality_score(normalized)
        assert 0.0 <= score <= 1.0
        print(f"✅ Quality score calculation working: {score:.2f}")
        
        # Test batch validation
        tokens = [valid_token, invalid_token]
        valid, invalid = validator.batch_validate(tokens)
        assert len(valid) == 1
        assert len(invalid) == 1
        print("✅ Batch validation working")
        
        # Test duplicate detection
        tokens_with_dupes = [
            {"contract_address": "0x1234", "chain": "ethereum"},
            {"contract_address": "0x1234", "chain": "ethereum"},  # Duplicate
            {"contract_address": "0x5678", "chain": "ethereum"},
        ]
        
        unique = validator.detect_duplicates(tokens_with_dupes)
        assert len(unique) == 2
        print("✅ Duplicate detection working")
        
    except Exception as e:
        print(f"❌ Error in data validator test: {e}")
        return False
    
    return True


async def main():
    """Run all tests"""
    print("🚀 Starting Token Discovery Service Tests\n")
    
    tests = [
        ("Rate Limiter", test_rate_limiter),
        ("Data Validator", test_data_validator),
        ("Token Discovery", test_token_discovery),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running {test_name} Tests")
        print('='*50)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                print(f"✅ {test_name} tests PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} tests FAILED")
        except Exception as e:
            print(f"❌ {test_name} tests FAILED with exception: {e}")
    
    print(f"\n{'='*50}")
    print(f"TEST SUMMARY: {passed}/{total} test suites passed")
    print('='*50)
    
    if passed == total:
        print("🎉 All tests passed! Token Discovery Service is ready.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the output above.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
