#!/usr/bin/env python3
"""
System Integration Test - Verify all components work together
"""

import asyncio
import sys
import os
from datetime import datetime


def test_imports():
    """Test that all modules can be imported"""
    print("📦 Testing Module Imports...")
    
    imports_to_test = [
        ("shared.config.settings", "settings"),
        ("shared.utils.logging", "get_logger"),
        ("services.data_sources.real_token_discovery", "RealTokenDiscoveryService"),
        ("services.data_sources.real_contract_analysis", "RealContractAnalysisService"),
        ("services.data_sources.real_onchain_data", "RealOnChainDataService"),
        ("services.data_sources.real_social_data", "RealSocialDataService"),
    ]

    # Test agent imports separately due to hyphenated directory
    agent_imports = [
        "token_hunter.TokenHunterAgent",
        "contract_auditor.ContractAuditorAgent",
        "onchain_analyst.OnChainAnalystAgent",
        "social_sentiment.SocialSentimentAgent",
        "workflow_orchestrator.WorkflowOrchestrator",
    ]
    
    passed = 0
    total = len(imports_to_test)
    
    # Test regular imports
    for module_name, class_name in imports_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {module_name}.{class_name}")
            passed += 1
        except Exception as e:
            print(f"❌ {module_name}.{class_name}: {e}")

    # Test agent imports with sys.path manipulation
    import sys
    agent_path = os.path.join(os.getcwd(), "services", "langgraph-agents")
    if agent_path not in sys.path:
        sys.path.insert(0, agent_path)

    for agent_import in agent_imports:
        try:
            module_name, class_name = agent_import.split(".")
            module = __import__(module_name)
            getattr(module, class_name)
            print(f"✅ services.langgraph-agents.{agent_import}")
            passed += 1
        except Exception as e:
            print(f"❌ services.langgraph-agents.{agent_import}: {e}")

    total = len(imports_to_test) + len(agent_imports)
    print(f"\nImport Summary: {passed}/{total} successful")
    return passed == total


def test_configuration():
    """Test configuration system"""
    print("\n⚙️  Testing Configuration System...")
    
    try:
        from shared.config.settings import settings
        
        # Test that settings object exists
        assert hasattr(settings, 'api'), "Settings should have api attribute"
        
        # Test DeepSeek model configuration
        model = getattr(settings.api, 'openrouter_model', None)
        if model:
            print(f"✅ OpenRouter model configured: {model}")
        else:
            print("✅ OpenRouter model using default: deepseek/deepseek-r1-0528:free")
        
        # Test base URL configuration
        base_url = getattr(settings.api, 'openrouter_base_url', None)
        if base_url:
            print(f"✅ OpenRouter base URL: {base_url}")
        else:
            print("✅ OpenRouter base URL using default")
        
        print("✅ Configuration system working")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def test_data_services():
    """Test data service initialization"""
    print("\n🔍 Testing Data Services...")
    
    services_passed = 0
    
    # Test Token Discovery Service
    try:
        from services.data_sources.real_token_discovery import RealTokenDiscoveryService
        service = RealTokenDiscoveryService()
        assert hasattr(service, 'discover_new_tokens'), "Should have discover_new_tokens method"
        print("✅ Token Discovery Service")
        services_passed += 1
    except Exception as e:
        print(f"❌ Token Discovery Service: {e}")
    
    # Test Contract Analysis Service
    try:
        from services.data_sources.real_contract_analysis import RealContractAnalysisService
        service = RealContractAnalysisService()
        assert hasattr(service, 'analyze_contract'), "Should have analyze_contract method"
        print("✅ Contract Analysis Service")
        services_passed += 1
    except Exception as e:
        print(f"❌ Contract Analysis Service: {e}")
    
    # Test On-Chain Data Service
    try:
        from services.data_sources.real_onchain_data import RealOnChainDataService
        service = RealOnChainDataService()
        assert hasattr(service, 'analyze_onchain_behavior'), "Should have analyze_onchain_behavior method"
        print("✅ On-Chain Data Service")
        services_passed += 1
    except Exception as e:
        print(f"❌ On-Chain Data Service: {e}")
    
    # Test Social Data Service
    try:
        from services.data_sources.real_social_data import RealSocialDataService
        service = RealSocialDataService()
        assert hasattr(service, 'analyze_social_sentiment'), "Should have analyze_social_sentiment method"
        print("✅ Social Data Service")
        services_passed += 1
    except Exception as e:
        print(f"❌ Social Data Service: {e}")
    
    print(f"\nData Services Summary: {services_passed}/4 working")
    return services_passed == 4


def test_agents():
    """Test agent initialization"""
    print("\n🤖 Testing Agent Initialization...")
    
    agents_passed = 0
    
    # Add agent path to sys.path
    import sys
    agent_path = os.path.join(os.getcwd(), "services", "langgraph-agents")
    if agent_path not in sys.path:
        sys.path.insert(0, agent_path)

    # Test Token Hunter Agent
    try:
        import token_hunter
        agent = token_hunter.TokenHunterAgent()
        assert hasattr(agent, 'hunt_tokens'), "Should have hunt_tokens method"
        print("✅ Token Hunter Agent")
        agents_passed += 1
    except Exception as e:
        print(f"❌ Token Hunter Agent: {e}")

    # Test Contract Auditor Agent
    try:
        import contract_auditor
        agent = contract_auditor.ContractAuditorAgent()
        assert hasattr(agent, 'audit_contract'), "Should have audit_contract method"
        print("✅ Contract Auditor Agent")
        agents_passed += 1
    except Exception as e:
        print(f"❌ Contract Auditor Agent: {e}")

    # Test On-Chain Analyst Agent
    try:
        import onchain_analyst
        agent = onchain_analyst.OnChainAnalystAgent()
        assert hasattr(agent, 'analyze_onchain_behavior'), "Should have analyze_onchain_behavior method"
        print("✅ On-Chain Analyst Agent")
        agents_passed += 1
    except Exception as e:
        print(f"❌ On-Chain Analyst Agent: {e}")

    # Test Social Sentiment Agent
    try:
        import social_sentiment
        agent = social_sentiment.SocialSentimentAgent()
        assert hasattr(agent, 'analyze_social_sentiment'), "Should have analyze_social_sentiment method"
        print("✅ Social Sentiment Agent")
        agents_passed += 1
    except Exception as e:
        print(f"❌ Social Sentiment Agent: {e}")

    # Test Workflow Orchestrator
    try:
        import workflow_orchestrator
        orchestrator = workflow_orchestrator.WorkflowOrchestrator()
        assert hasattr(orchestrator, 'analyze_token'), "Should have analyze_token method"
        print("✅ Workflow Orchestrator")
        agents_passed += 1
    except Exception as e:
        print(f"❌ Workflow Orchestrator: {e}")
    
    print(f"\nAgent Summary: {agents_passed}/5 working")
    return agents_passed == 5


def test_deepseek_configuration():
    """Test DeepSeek R1 model configuration"""
    print("\n🧠 Testing DeepSeek R1 Configuration...")
    
    try:
        from langchain_openai import ChatOpenAI
        
        # Test that we can initialize the model (without making API calls)
        llm = ChatOpenAI(
            model="deepseek/deepseek-r1-0528:free",
            openai_api_key="test-key",  # Dummy key for initialization test
            openai_api_base="https://openrouter.ai/api/v1",
            temperature=0.1,
            max_tokens=100
        )
        
        assert llm.model_name == "deepseek/deepseek-r1-0528:free", "Should use DeepSeek R1 model"
        assert llm.openai_api_base == "https://openrouter.ai/api/v1", "Should use OpenRouter base URL"
        
        print("✅ DeepSeek R1 model configuration correct")
        print(f"   Model: {llm.model_name}")
        print(f"   Base URL: {llm.openai_api_base}")
        print(f"   Temperature: {llm.temperature}")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek configuration failed: {e}")
        return False


def check_api_keys():
    """Check API key configuration"""
    print("\n🔑 Checking API Key Configuration...")
    
    api_keys = {
        "OPENROUTER_API_KEY": os.getenv("OPENROUTER_API_KEY"),
        "ETHERSCAN_API_KEY": os.getenv("ETHERSCAN_API_KEY"),
        "COINGECKO_API_KEY": os.getenv("COINGECKO_API_KEY"),
        "REDDIT_CLIENT_ID": os.getenv("REDDIT_CLIENT_ID"),
        "REDDIT_CLIENT_SECRET": os.getenv("REDDIT_CLIENT_SECRET"),
        "TWITTER_BEARER_TOKEN": os.getenv("TWITTER_BEARER_TOKEN"),
    }
    
    configured_count = 0
    for key_name, key_value in api_keys.items():
        if key_value:
            print(f"✅ {key_name}: Configured")
            configured_count += 1
        else:
            print(f"❌ {key_name}: Not configured")
    
    print(f"\nAPI Keys: {configured_count}/{len(api_keys)} configured")
    
    if configured_count == 0:
        print("\n⚠️  No API keys configured!")
        print("   System will use fallback/mock data.")
        print("   For real data integration, set these environment variables:")
        for key_name in api_keys.keys():
            print(f"   export {key_name}=your_key_here")
    
    return configured_count


def main():
    """Run complete system integration test"""
    print("🚀 Quantum Market Intelligence - System Integration Test")
    print("=" * 70)
    print(f"📅 {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"🐍 Python: {sys.version}")
    print(f"📁 Working Directory: {os.getcwd()}")
    
    tests = [
        ("Module Imports", test_imports),
        ("Configuration System", test_configuration),
        ("Data Services", test_data_services),
        ("Agent Initialization", test_agents),
        ("DeepSeek R1 Configuration", test_deepseek_configuration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            
            if result:
                print(f"\n✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"\n❌ {test_name} FAILED")
        except Exception as e:
            print(f"\n❌ {test_name} FAILED with exception: {e}")
            import traceback
            traceback.print_exc()
    
    # Check API configuration (informational)
    configured_apis = check_api_keys()
    
    print(f"\n{'='*70}")
    print(f"SYSTEM INTEGRATION TEST SUMMARY")
    print('='*70)
    print(f"Core Tests: {passed}/{total} passed")
    print(f"API Keys: {configured_apis}/6 configured")
    
    if passed == total:
        print("\n🎉 SYSTEM INTEGRATION SUCCESSFUL!")
        print("\n✅ All core components working:")
        print("  • DeepSeek R1 model configured")
        print("  • Real data services implemented")
        print("  • All 4 LangGraph agents ready")
        print("  • Workflow orchestrator functional")
        print("  • Fallback mechanisms in place")
        
        if configured_apis > 0:
            print(f"\n🔑 Real API Integration: {configured_apis}/6 APIs configured")
            print("   System ready for production with real data!")
        else:
            print("\n⚠️  Mock Data Mode: No API keys configured")
            print("   System will use fallback data for testing")
        
        print("\n🚀 READY FOR PHASE 3: Advanced Rug Detection Engine")
        return True
    else:
        print(f"\n⚠️  SYSTEM INTEGRATION ISSUES: {total - passed} tests failed")
        print("   Please fix the issues above before proceeding to Phase 3")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
