#!/usr/bin/env python3
"""
Integration test for advanced rate limiting and circuit breakers
"""

import asyncio
import time
from datetime import datetime


async def test_advanced_rate_limiting():
    """Test advanced rate limiting features"""
    print("🧪 Testing Advanced Rate Limiting...")
    
    class AdvancedRateLimiter:
        def __init__(self):
            self.api_configs = {
                "dexscreener": {
                    "requests_per_minute": 100,
                    "requests_per_second": 2,
                    "burst_limit": 10,
                    "strategy": "token_bucket"
                },
                "etherscan": {
                    "requests_per_minute": 300,
                    "requests_per_second": 5,
                    "burst_limit": 20,
                    "strategy": "token_bucket"
                },
                "coingecko": {
                    "requests_per_minute": 50,
                    "requests_per_second": 1,
                    "burst_limit": 5,
                    "strategy": "sliding_window"
                }
            }
            
            self.buckets = {}
            self.sliding_windows = {}
            self.backoff_states = {}
            
            # Initialize buckets and states
            for api_name, config in self.api_configs.items():
                self.buckets[api_name] = {
                    "tokens": config["requests_per_minute"],
                    "capacity": config["requests_per_minute"],
                    "last_refill": time.time(),
                    "refill_rate": config["requests_per_minute"] / 60.0
                }
                
                self.sliding_windows[api_name] = {}
                
                self.backoff_states[api_name] = {
                    "consecutive_failures": 0,
                    "last_failure_time": None,
                    "current_backoff": 0,
                    "next_allowed_time": 0
                }
        
        async def acquire_token_bucket(self, api_name, tokens=1):
            """Token bucket rate limiting"""
            if api_name not in self.buckets:
                return True
            
            bucket = self.buckets[api_name]
            current_time = time.time()
            
            # Refill bucket
            time_elapsed = current_time - bucket["last_refill"]
            tokens_to_add = time_elapsed * bucket["refill_rate"]
            
            bucket["tokens"] = min(bucket["capacity"], bucket["tokens"] + tokens_to_add)
            bucket["last_refill"] = current_time
            
            # Check if we have enough tokens
            if bucket["tokens"] >= tokens:
                bucket["tokens"] -= tokens
                return True
            return False
        
        async def acquire_sliding_window(self, api_name, tokens=1):
            """Sliding window rate limiting"""
            if api_name not in self.api_configs:
                return True
            
            config = self.api_configs[api_name]
            current_minute = int(time.time() // 60)
            
            # Clean old windows
            window = self.sliding_windows[api_name]
            old_minutes = [m for m in window.keys() if m < current_minute - 1]
            for old_minute in old_minutes:
                del window[old_minute]
            
            # Count requests in current window
            current_count = window.get(current_minute, 0)
            
            if current_count + tokens <= config["requests_per_minute"]:
                window[current_minute] = current_count + tokens
                return True
            return False
        
        async def acquire(self, api_name, tokens=1):
            """Main acquire method with strategy selection"""
            if api_name not in self.api_configs:
                return True
            
            # Check exponential backoff
            if not await self._check_backoff(api_name):
                return False
            
            config = self.api_configs[api_name]
            
            if config["strategy"] == "token_bucket":
                return await self.acquire_token_bucket(api_name, tokens)
            elif config["strategy"] == "sliding_window":
                return await self.acquire_sliding_window(api_name, tokens)
            
            return True
        
        async def _check_backoff(self, api_name):
            """Check exponential backoff"""
            backoff_state = self.backoff_states[api_name]
            current_time = time.time()
            
            if current_time < backoff_state["next_allowed_time"]:
                return False
            return True
        
        async def record_failure(self, api_name, error_type="generic"):
            """Record API failure and apply exponential backoff"""
            if api_name not in self.backoff_states:
                return
            
            backoff_state = self.backoff_states[api_name]
            backoff_state["consecutive_failures"] += 1
            backoff_state["last_failure_time"] = time.time()
            
            # Calculate exponential backoff (max 300 seconds)
            backoff_seconds = min(2 ** backoff_state["consecutive_failures"], 300)
            backoff_state["current_backoff"] = backoff_seconds
            backoff_state["next_allowed_time"] = time.time() + backoff_seconds
        
        async def record_success(self, api_name):
            """Record success and reset backoff"""
            if api_name in self.backoff_states:
                backoff_state = self.backoff_states[api_name]
                backoff_state["consecutive_failures"] = 0
                backoff_state["current_backoff"] = 0
                backoff_state["next_allowed_time"] = 0
        
        async def get_status(self, api_name):
            """Get rate limit status"""
            if api_name not in self.api_configs:
                return {"error": "Unknown API"}
            
            config = self.api_configs[api_name]
            bucket = self.buckets.get(api_name, {})
            backoff_state = self.backoff_states.get(api_name, {})
            
            # Calculate available tokens for token bucket
            if config["strategy"] == "token_bucket" and bucket:
                current_time = time.time()
                time_elapsed = current_time - bucket["last_refill"]
                tokens_to_add = time_elapsed * bucket["refill_rate"]
                available_tokens = min(bucket["capacity"], bucket["tokens"] + tokens_to_add)
            else:
                available_tokens = 0
            
            return {
                "api_name": api_name,
                "strategy": config["strategy"],
                "requests_per_minute": config["requests_per_minute"],
                "available_tokens": available_tokens,
                "consecutive_failures": backoff_state.get("consecutive_failures", 0),
                "in_backoff": time.time() < backoff_state.get("next_allowed_time", 0),
                "backoff_remaining": max(0, backoff_state.get("next_allowed_time", 0) - time.time())
            }
    
    limiter = AdvancedRateLimiter()
    
    # Test token bucket strategy
    print("Testing token bucket strategy...")

    # Should initially allow requests
    assert await limiter.acquire("dexscreener", 1), "Should allow initial request"
    assert await limiter.acquire("dexscreener", 5), "Should allow burst requests"
    print("✅ Token bucket initial requests working")

    # Exhaust bucket by requesting all available tokens at once
    bucket = limiter.buckets["dexscreener"]
    available_tokens = int(bucket["tokens"])

    # Request all available tokens plus some extra
    result = await limiter.acquire("dexscreener", available_tokens + 50)

    # Should be rate limited now since we requested more than available
    assert not result, "Should be rate limited when requesting more than available tokens"
    print("✅ Token bucket exhaustion working")
    
    # Test sliding window strategy
    print("Testing sliding window strategy...")
    
    # Should initially allow requests
    assert await limiter.acquire("coingecko", 1), "Should allow initial request"
    
    # Fill up the window
    for _ in range(49):  # 49 more to reach limit of 50
        await limiter.acquire("coingecko", 1)
    
    # Should be at limit now
    assert not await limiter.acquire("coingecko", 1), "Should be rate limited at window limit"
    print("✅ Sliding window rate limiting working")
    
    # Test exponential backoff
    print("Testing exponential backoff...")
    
    # Record failures
    await limiter.record_failure("etherscan")
    await limiter.record_failure("etherscan")
    
    # Should be in backoff
    status = await limiter.get_status("etherscan")
    assert status["consecutive_failures"] == 2, "Should track consecutive failures"
    assert status["in_backoff"], "Should be in backoff period"
    print("✅ Exponential backoff working")
    
    # Test success reset
    await limiter.record_success("etherscan")
    status = await limiter.get_status("etherscan")
    assert status["consecutive_failures"] == 0, "Should reset failures on success"
    assert not status["in_backoff"], "Should not be in backoff after success"
    print("✅ Success reset working")
    
    return True


async def test_circuit_breaker_integration():
    """Test circuit breaker integration with rate limiting"""
    print("🧪 Testing Circuit Breaker Integration...")
    
    class IntegratedCircuitBreaker:
        def __init__(self):
            self.circuit_states = {}
            self.rate_limiter = None  # Would be injected
        
        def initialize_circuit(self, api_name, failure_threshold=5, recovery_timeout=60):
            """Initialize circuit breaker for an API"""
            self.circuit_states[api_name] = {
                "state": "closed",  # closed, open, half_open
                "failures": 0,
                "failure_threshold": failure_threshold,
                "last_failure_time": None,
                "recovery_timeout": recovery_timeout,
                "success_count": 0,
                "half_open_max_requests": 3
            }
        
        def can_execute(self, api_name):
            """Check if request can be executed"""
            if api_name not in self.circuit_states:
                self.initialize_circuit(api_name)
            
            circuit = self.circuit_states[api_name]
            current_time = time.time()
            
            if circuit["state"] == "closed":
                return True
            elif circuit["state"] == "open":
                # Check if recovery timeout has passed
                if (circuit["last_failure_time"] and 
                    current_time - circuit["last_failure_time"] > circuit["recovery_timeout"]):
                    circuit["state"] = "half_open"
                    circuit["success_count"] = 0
                    return True
                return False
            elif circuit["state"] == "half_open":
                return circuit["success_count"] < circuit["half_open_max_requests"]
            
            return False
        
        def record_success(self, api_name):
            """Record successful execution"""
            if api_name not in self.circuit_states:
                return
            
            circuit = self.circuit_states[api_name]
            
            if circuit["state"] == "half_open":
                circuit["success_count"] += 1
                if circuit["success_count"] >= circuit["half_open_max_requests"]:
                    circuit["state"] = "closed"
                    circuit["failures"] = 0
            elif circuit["state"] == "closed":
                circuit["failures"] = 0  # Reset failure count on success
        
        def record_failure(self, api_name):
            """Record failed execution"""
            if api_name not in self.circuit_states:
                self.initialize_circuit(api_name)
            
            circuit = self.circuit_states[api_name]
            circuit["failures"] += 1
            circuit["last_failure_time"] = time.time()
            
            if circuit["failures"] >= circuit["failure_threshold"]:
                circuit["state"] = "open"
            elif circuit["state"] == "half_open":
                circuit["state"] = "open"  # Go back to open on failure in half_open
        
        def get_circuit_status(self, api_name):
            """Get circuit breaker status"""
            if api_name not in self.circuit_states:
                return {"state": "unknown"}
            
            circuit = self.circuit_states[api_name]
            return {
                "state": circuit["state"],
                "failures": circuit["failures"],
                "failure_threshold": circuit["failure_threshold"],
                "can_execute": self.can_execute(api_name)
            }
    
    breaker = IntegratedCircuitBreaker()
    
    # Test initial state
    assert breaker.can_execute("test_api"), "Should initially allow execution"
    status = breaker.get_circuit_status("test_api")
    assert status["state"] == "closed", "Should initially be closed"
    print("✅ Initial circuit state working")
    
    # Test failure accumulation
    for i in range(5):  # Reach failure threshold
        breaker.record_failure("test_api")
    
    assert not breaker.can_execute("test_api"), "Should not allow execution when open"
    status = breaker.get_circuit_status("test_api")
    assert status["state"] == "open", "Should be open after failures"
    print("✅ Circuit opening on failures working")
    
    # Test recovery timeout (simulate time passing)
    breaker.circuit_states["test_api"]["last_failure_time"] = time.time() - 61  # 61 seconds ago
    
    assert breaker.can_execute("test_api"), "Should allow execution after recovery timeout"
    status = breaker.get_circuit_status("test_api")
    assert status["state"] == "half_open", "Should be half_open after recovery timeout"
    print("✅ Circuit recovery working")
    
    # Test half_open to closed transition
    for i in range(3):  # Success in half_open state
        breaker.record_success("test_api")
    
    status = breaker.get_circuit_status("test_api")
    assert status["state"] == "closed", "Should be closed after successful half_open period"
    print("✅ Half_open to closed transition working")
    
    return True


async def test_api_health_monitoring():
    """Test API health monitoring"""
    print("🧪 Testing API Health Monitoring...")
    
    class APIHealthMonitor:
        def __init__(self):
            self.api_metrics = {}
        
        def initialize_api(self, api_name):
            """Initialize metrics for an API"""
            self.api_metrics[api_name] = {
                "total_requests": 0,
                "successful_requests": 0,
                "failed_requests": 0,
                "avg_response_time": 0,
                "last_request_time": None,
                "error_rate": 0,
                "uptime_percentage": 100,
                "consecutive_failures": 0,
                "max_consecutive_failures": 0,
                "response_times": []  # Keep last 100 response times
            }
        
        def record_request(self, api_name, success, response_time, error=None):
            """Record API request metrics"""
            if api_name not in self.api_metrics:
                self.initialize_api(api_name)
            
            metrics = self.api_metrics[api_name]
            metrics["total_requests"] += 1
            metrics["last_request_time"] = datetime.utcnow()
            
            # Record response time
            metrics["response_times"].append(response_time)
            if len(metrics["response_times"]) > 100:
                metrics["response_times"].pop(0)  # Keep only last 100
            
            # Calculate average response time
            metrics["avg_response_time"] = sum(metrics["response_times"]) / len(metrics["response_times"])
            
            if success:
                metrics["successful_requests"] += 1
                metrics["consecutive_failures"] = 0
            else:
                metrics["failed_requests"] += 1
                metrics["consecutive_failures"] += 1
                metrics["max_consecutive_failures"] = max(
                    metrics["max_consecutive_failures"],
                    metrics["consecutive_failures"]
                )
            
            # Calculate error rate
            metrics["error_rate"] = (metrics["failed_requests"] / metrics["total_requests"]) * 100
            
            # Calculate uptime percentage
            metrics["uptime_percentage"] = (metrics["successful_requests"] / metrics["total_requests"]) * 100
        
        def get_health_status(self, api_name):
            """Get health status for an API"""
            if api_name not in self.api_metrics:
                return {"status": "unknown", "reason": "No metrics available"}
            
            metrics = self.api_metrics[api_name]
            
            # Determine health based on multiple factors
            is_healthy = (
                metrics["error_rate"] < 10 and  # Less than 10% error rate
                metrics["consecutive_failures"] < 5 and  # Less than 5 consecutive failures
                metrics["avg_response_time"] < 5000  # Less than 5 second average response time
            )
            
            health_score = 100
            if metrics["error_rate"] > 5:
                health_score -= metrics["error_rate"] * 2
            if metrics["consecutive_failures"] > 0:
                health_score -= metrics["consecutive_failures"] * 5
            if metrics["avg_response_time"] > 1000:
                health_score -= (metrics["avg_response_time"] - 1000) / 100
            
            health_score = max(0, health_score)
            
            return {
                "status": "healthy" if is_healthy else "unhealthy",
                "health_score": health_score,
                "metrics": metrics,
                "recommendations": self._get_recommendations(metrics)
            }
        
        def _get_recommendations(self, metrics):
            """Get recommendations based on metrics"""
            recommendations = []
            
            if metrics["error_rate"] > 10:
                recommendations.append("High error rate detected - check API endpoint")
            if metrics["consecutive_failures"] > 3:
                recommendations.append("Multiple consecutive failures - implement circuit breaker")
            if metrics["avg_response_time"] > 2000:
                recommendations.append("Slow response times - consider caching or timeout adjustment")
            
            return recommendations
        
        def get_overall_health(self):
            """Get overall health across all APIs"""
            if not self.api_metrics:
                return {"status": "unknown", "reason": "No APIs monitored"}
            
            healthy_apis = 0
            total_apis = len(self.api_metrics)
            
            for api_name in self.api_metrics:
                health = self.get_health_status(api_name)
                if health["status"] == "healthy":
                    healthy_apis += 1
            
            health_percentage = (healthy_apis / total_apis) * 100
            
            return {
                "status": "healthy" if health_percentage >= 80 else "degraded" if health_percentage >= 50 else "unhealthy",
                "healthy_apis": healthy_apis,
                "total_apis": total_apis,
                "health_percentage": health_percentage
            }
    
    monitor = APIHealthMonitor()
    
    # Test initial state
    health = monitor.get_overall_health()
    assert health["status"] == "unknown", "Should be unknown with no APIs"
    print("✅ Initial health state working")
    
    # Record successful requests
    for i in range(10):
        monitor.record_request("test_api", success=True, response_time=100 + i * 10)
    
    health = monitor.get_health_status("test_api")
    assert health["status"] == "healthy", "Should be healthy with successful requests"
    assert health["metrics"]["error_rate"] == 0, "Should have 0% error rate"
    assert health["metrics"]["avg_response_time"] < 200, "Should have low average response time"
    print("✅ Healthy API status working")
    
    # Record some failures
    for i in range(5):
        monitor.record_request("test_api", success=False, response_time=5000, error="Timeout")
    
    health = monitor.get_health_status("test_api")
    assert health["status"] == "unhealthy", "Should be unhealthy with failures"
    assert health["metrics"]["error_rate"] > 0, "Should have non-zero error rate"
    assert len(health["recommendations"]) > 0, "Should have recommendations"
    print("✅ Unhealthy API status working")
    
    # Test overall health
    monitor.initialize_api("healthy_api")
    for i in range(10):
        monitor.record_request("healthy_api", success=True, response_time=100)
    
    overall_health = monitor.get_overall_health()
    assert overall_health["total_apis"] == 2, "Should track 2 APIs"
    assert overall_health["healthy_apis"] == 1, "Should have 1 healthy API"
    print("✅ Overall health monitoring working")
    
    return True


async def main():
    """Run all advanced rate limiting tests"""
    print("🚀 Starting Advanced Rate Limiting & Circuit Breaker Tests\n")
    
    tests = [
        ("Advanced Rate Limiting", test_advanced_rate_limiting),
        ("Circuit Breaker Integration", test_circuit_breaker_integration),
        ("API Health Monitoring", test_api_health_monitoring),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Running {test_name} Tests")
        print('='*60)
        
        try:
            result = await test_func()
            
            if result:
                print(f"✅ {test_name} tests PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} tests FAILED")
        except Exception as e:
            print(f"❌ {test_name} tests FAILED with exception: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*60}")
    print(f"TEST SUMMARY: {passed}/{total} test suites passed")
    print('='*60)
    
    if passed == total:
        print("🎉 All advanced rate limiting tests passed!")
        print("\n📊 Key Features Validated:")
        print("  • Token bucket and sliding window rate limiting")
        print("  • Exponential backoff with failure tracking")
        print("  • Circuit breaker with half-open recovery")
        print("  • Comprehensive API health monitoring")
        print("  • Multi-strategy rate limiting support")
        return True
    else:
        print("⚠️  Some tests failed. Please review the output above.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
