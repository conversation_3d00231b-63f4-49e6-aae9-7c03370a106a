#!/usr/bin/env python3
"""
Test script to verify API Gateway can start and respond to requests
"""

import asyncio
import sys
import os
import time
import subprocess
import signal
from contextlib import asynccontextmanager

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import httpx


async def test_api_gateway():
    """Test API Gateway startup and basic endpoints"""
    
    print("=" * 60)
    print("QUANTUM MARKET INTELLIGENCE - API GATEWAY TEST")
    print("=" * 60)
    
    # Start the API Gateway in a subprocess
    print("1. Starting API Gateway...")
    
    env = os.environ.copy()
    env.pop('JWT_SECRET_KEY', None)  # Remove system env var to use .env file
    
    process = subprocess.Popen([
        sys.executable, "-c", """
import sys
sys.path.insert(0, '.')
from services.api_gateway.main import main
main()
"""], 
        cwd=os.path.dirname(os.path.abspath(__file__)),
        env=env,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        preexec_fn=os.setsid
    )
    
    # Wait for the server to start
    print("   Waiting for server to start...")
    await asyncio.sleep(5)
    
    try:
        # Test basic endpoints
        async with httpx.AsyncClient(timeout=10.0) as client:
            
            # Test root endpoint
            print("2. Testing root endpoint...")
            try:
                response = await client.get("http://localhost:8000/")
                if response.status_code == 200:
                    print("   ✓ Root endpoint accessible")
                    data = response.json()
                    print(f"   Service: {data.get('name')}")
                    print(f"   Version: {data.get('version')}")
                else:
                    print(f"   ✗ Root endpoint failed: HTTP {response.status_code}")
                    return False
            except Exception as e:
                print(f"   ✗ Root endpoint failed: {e}")
                return False
            
            # Test health endpoint
            print("3. Testing health endpoint...")
            try:
                response = await client.get("http://localhost:8000/health/")
                if response.status_code == 200:
                    print("   ✓ Health endpoint accessible")
                    data = response.json()
                    print(f"   Status: {data.get('status')}")
                else:
                    print(f"   ✗ Health endpoint failed: HTTP {response.status_code}")
                    return False
            except Exception as e:
                print(f"   ✗ Health endpoint failed: {e}")
                return False
            
            # Test detailed health endpoint
            print("4. Testing detailed health endpoint...")
            try:
                response = await client.get("http://localhost:8000/health/detailed")
                if response.status_code in [200, 503]:  # 503 is OK if some services are down
                    print("   ✓ Detailed health endpoint accessible")
                    data = response.json()
                    print(f"   Overall status: {data.get('status')}")
                    
                    services = data.get('services', {})
                    for service, status in services.items():
                        if isinstance(status, dict):
                            service_status = status.get('status', 'unknown')
                            print(f"   - {service}: {service_status}")
                        else:
                            print(f"   - {service}: {status}")
                else:
                    print(f"   ✗ Detailed health endpoint failed: HTTP {response.status_code}")
                    return False
            except Exception as e:
                print(f"   ✗ Detailed health endpoint failed: {e}")
                return False
            
            # Test tokens endpoint
            print("5. Testing tokens endpoint...")
            try:
                response = await client.get("http://localhost:8000/api/v1/tokens/")
                if response.status_code == 200:
                    print("   ✓ Tokens endpoint accessible")
                    data = response.json()
                    print(f"   Total items: {data.get('total', 0)}")
                else:
                    print(f"   ✗ Tokens endpoint failed: HTTP {response.status_code}")
                    return False
            except Exception as e:
                print(f"   ✗ Tokens endpoint failed: {e}")
                return False
            
            # Test analysis endpoint
            print("6. Testing analysis endpoint...")
            try:
                response = await client.get("http://localhost:8000/api/v1/analysis/alerts")
                if response.status_code == 200:
                    print("   ✓ Analysis endpoint accessible")
                    data = response.json()
                    print(f"   Active alerts: {len(data.get('alerts', []))}")
                else:
                    print(f"   ✗ Analysis endpoint failed: HTTP {response.status_code}")
                    return False
            except Exception as e:
                print(f"   ✗ Analysis endpoint failed: {e}")
                return False
        
        print()
        print("🎉 All API Gateway tests passed!")
        return True
        
    finally:
        # Clean up: terminate the server process
        print("\n7. Shutting down API Gateway...")
        try:
            os.killpg(os.getpgid(process.pid), signal.SIGTERM)
            process.wait(timeout=5)
        except:
            try:
                os.killpg(os.getpgid(process.pid), signal.SIGKILL)
            except:
                pass
        print("   ✓ API Gateway shut down")


async def main():
    """Main test function"""
    try:
        success = await test_api_gateway()
        return 0 if success else 1
    except Exception as e:
        print(f"Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
